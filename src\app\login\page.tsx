
'use client';

import { useState, type FormEvent } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertTriangle, LogIn, Loader2 } from 'lucide-react';
import { Logo } from '@/components/navigation/Logo';
import Link from 'next/link';
import type { AuthError } from 'firebase/auth';

export default function LoginPage() {
  const { login, isLoading: authIsLoading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError('');
    setIsSubmitting(true);

    if (!email || !password) {
      setError('Please enter email and password.');
      setIsSubmitting(false);
      return;
    }

    const result = await login(email, password);

    if (!result.success) {
      let displayMessage = 'Login failed. Please check your credentials or try again later.';
      if (result.error) {
        const firebaseError = result.error as AuthError; // Cast to AuthError for code property
        if (['auth/invalid-credential', 'auth/wrong-password', 'auth/user-not-found'].includes(firebaseError.code)) {
          displayMessage = 'Invalid email or password. Please try again.';
        } else if (firebaseError.message) {
          // Use Firebase's message for other types of errors (e.g., network issues)
          displayMessage = firebaseError.message;
        }
      }
      setError(displayMessage);
    }
    // On success, navigation is handled by AuthContext's useEffect

    setIsSubmitting(false);
  };

  const currentIsLoading = authIsLoading || isSubmitting;

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background p-4">
      <div className="mb-8">
        <Logo size="lg" />
      </div>
      <Card className="w-full max-w-md shadow-xl">
        <CardHeader className="text-center">
          <CardTitle className="font-headline text-3xl">Welcome Back</CardTitle>
          <CardDescription>Sign in to access your EduLite account.</CardDescription>
        </CardHeader>
        <CardContent>
           {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Login Failed</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    disabled={currentIsLoading}
                />
                </div>
                <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    disabled={currentIsLoading}
                />
                </div>
                <Button type="submit" className="w-full bg-primary hover:bg-primary/90 text-primary-foreground" disabled={currentIsLoading}>
                {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <LogIn className="mr-2 h-4 w-4" />}
                Sign In
                </Button>
            </form>
          
          <p className="mt-6 text-center text-sm text-muted-foreground">
            Don&apos;t have an account?{' '}
            <Link href="/apply" className="font-medium text-accent hover:underline">
              Apply Now
            </Link>
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
