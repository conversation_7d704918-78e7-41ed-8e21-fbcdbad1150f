
'use server';

import { Resend } from 'resend';
import React from 'react';

const fromEmail = process.env.RESEND_FROM_EMAIL;

export const sendEmail = async ({
  to,
  subject,
  reactElement,
}: {
  to: string | string[];
  subject: string;
  reactElement: React.ReactElement;
}) => {
  const apiKey = process.env.RESEND_API_KEY;

  if (!apiKey) {
    console.error('RESEND_API_KEY is not set. Skipping email sending.');
    return { success: false, error: 'RESEND_API_KEY not configured.' };
  }
  if (!fromEmail) {
    console.error('RESEND_FROM_EMAIL is not set. Skipping email sending.');
    return { success: false, error: 'RESEND_FROM_EMAIL not configured.' };
  }

  console.log('Email configuration:', {
    apiKey: apiKey ? `${apiKey.substring(0, 10)}...` : 'NOT SET',
    fromEmail,
    to: Array.isArray(to) ? to.join(', ') : to,
    subject
  });
  
  try {
    const resend = new Resend(apiKey);
    const { data, error } = await resend.emails.send({
      from: fromEmail,
      to: to,
      subject: subject,
      react: reactElement,
    });

    if (error) {
      console.error('Error sending email via Resend:', error);
      console.error('Full error details:', JSON.stringify(error, null, 2));
      // Return a structured error response
      return { success: false, error: error.message || 'Unknown Resend error' };
    }

    console.log('Email sent successfully:', data);
    return { success: true, data };
  } catch (error) {
    console.error('Exception caught while sending email:', error);
    if (error instanceof Error) {
        return { success: false, error: error.message };
    }
    return { success: false, error: 'An unknown error occurred.' };
  }
};
