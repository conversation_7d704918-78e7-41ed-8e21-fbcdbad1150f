
'use client';

import Link from 'next/link';
import { GraduationCap } from 'lucide-react';

export function Logo({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {
  const sizeClasses = {
    sm: { container: 'h-6 w-6', text: 'text-xl' },
    md: { container: 'h-7 w-7', text: 'text-2xl' },
    lg: { container: 'h-8 w-8', text: 'text-3xl' },
  };

  return (
    <Link href="/" className="flex items-center gap-2 text-primary hover:opacity-80 transition-opacity">
      <div className={`relative ${sizeClasses[size].container}`}>
        <GraduationCap className={`text-accent ${sizeClasses[size].container}`} />
      </div>
      <span className={`font-headline font-semibold ${sizeClasses[size].text}`}>EduLite</span>
    </Link>
  );
}
