
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import type { Subject, StudyMaterial, Unit } from '@/types';
import React from 'react';
import { PlusCircle, Trash2, GripVertical } from 'lucide-react';

const materialTypes = ['PDF', 'Video', 'Link', 'Document', 'Text'] as const;

const studyMaterialSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(3, { message: 'Material name must be at least 3 characters.' }),
  type: z.enum(materialTypes, { required_error: "Please select a material type." }),
  url: z.string().url({ message: 'Please enter a valid URL.' }).optional().or(z.literal('')),
  content: z.string().optional(),
}).superRefine((data, ctx) => {
    if (data.type === 'Text') {
        if (!data.content || data.content.trim().length === 0) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "Content cannot be empty for Text type.",
                path: ['content'],
            });
        }
    } else {
        if (!data.url) {
             ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "A valid URL is required for this material type.",
                path: ['url'],
            });
        }
    }
});

const unitSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(3, { message: 'Unit name must be at least 3 characters.' }),
  materials: z.array(studyMaterialSchema).optional(),
});

const subjectFormSchema = z.object({
  name: z.string().min(3, { message: 'Subject name must be at least 3 characters.' }),
  credits: z.coerce.number().int().min(1, { message: 'Credits must be at least 1.' }).max(10, { message: 'Credits cannot exceed 10.' }),
  units: z.array(unitSchema).optional(),
});

type SubjectFormValues = z.infer<typeof subjectFormSchema>;

interface SubjectDialogProps {
  subject?: Subject | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (subjectData: Omit<Subject, 'id'> | Subject) => void;
}

export function SubjectDialog({ subject, open, onOpenChange, onSave }: SubjectDialogProps) {
  const form = useForm<SubjectFormValues>({
    resolver: zodResolver(subjectFormSchema),
    defaultValues: subject 
      ? { name: subject.name, credits: subject.credits, units: subject.units || [] } 
      : { name: '', credits: 1, units: [] },
  });

  const { fields: unitFields, append: appendUnit, remove: removeUnit } = useFieldArray({
    control: form.control,
    name: "units",
  });

  React.useEffect(() => {
    if (open) {
      if (subject) {
        form.reset({ name: subject.name, credits: subject.credits, units: subject.units || [] });
      } else {
        form.reset({ name: '', credits: 1, units: [] });
      }
    }
  }, [subject, form, open]);

  function onSubmit(data: SubjectFormValues) {
    const processedData = {
        ...data,
        units: data.units?.map(u => ({
            ...u,
            id: u.id || `unit-${Date.now()}-${Math.random().toString(36).substring(2,7)}`,
            materials: u.materials?.map(m => ({
                ...m,
                id: m.id || `mat-${Date.now()}-${Math.random().toString(36).substring(2,7)}`,
            }))
        }))
    };
    if (subject) {
      onSave({ ...subject, ...processedData });
    } else {
      onSave(processedData);
    }
    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="font-headline text-xl">{subject ? 'Edit Subject' : 'Add New Subject'}</DialogTitle>
          <DialogDescription>
            {subject ? 'Update the details for this subject.' : 'Fill in the details for the new subject.'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 flex-grow overflow-y-auto pr-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                        <FormItem className="md:col-span-2">
                        <FormLabel>Subject Name</FormLabel>
                        <FormControl>
                            <Input placeholder="e.g., Introduction to Programming" {...field} />
                        </FormControl>
                        <FormMessage />
                        </FormItem>
                    )}
                    />
                <FormField
                    control={form.control}
                    name="credits"
                    render={({ field }) => (
                        <FormItem>
                        <FormLabel>Credits</FormLabel>
                        <FormControl>
                            <Input type="number" min="1" max="10" placeholder="e.g., 4" {...field} />
                        </FormControl>
                        <FormMessage />
                        </FormItem>
                    )}
                    />
            </div>
            
            <Separator />

            <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Units</h3>
                 <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => appendUnit({ name: `Unit ${unitFields.length + 1}`, materials: [] })}
                >
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Add Unit
                </Button>
            </div>
            
            <Accordion type="multiple" className="w-full space-y-2">
            {unitFields.map((unitField, unitIndex) => (
                <AccordionItem value={`unit-${unitIndex}`} key={unitField.id} className="border rounded-md px-3">
                    <AccordionTrigger className="py-3 hover:no-underline">
                        <div className="flex items-center gap-2 flex-grow">
                             <GripVertical className="h-5 w-5 text-muted-foreground" />
                             <FormField
                                control={form.control}
                                name={`units.${unitIndex}.name`}
                                render={({ field }) => (
                                    <FormItem className="flex-grow">
                                    <FormControl><Input {...field} placeholder="Unit name" className="text-md" /></FormControl>
                                    <FormMessage />
                                    </FormItem>
                                )}
                             />
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="pl-4 pb-4">
                        <UnitMaterials control={form.control} watch={form.watch} unitIndex={unitIndex} />
                        <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="mt-4"
                            onClick={() => removeUnit(unitIndex)}
                        >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Unit
                        </Button>
                    </AccordionContent>
                </AccordionItem>
            ))}
            </Accordion>
            {unitFields.length === 0 && (
                <div className="text-sm text-muted-foreground text-center py-4 border-2 border-dashed rounded-lg">
                    No units defined for this subject.
                </div>
            )}
            
            <DialogFooter className="sticky bottom-0 bg-background pt-4 border-t">
              <DialogClose asChild>
                <Button type="button" variant="outline">Cancel</Button>
              </DialogClose>
              <Button type="submit" className="bg-primary hover:bg-primary/90 text-primary-foreground">
                {subject ? 'Save Changes' : 'Add Subject'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}


function UnitMaterials({ control, watch, unitIndex }: { control: any; watch: any; unitIndex: number }) {
  const { fields, append, remove } = useFieldArray({
    control,
    name: `units.${unitIndex}.materials`,
  });

  return (
    <div className="space-y-3">
      <h4 className="text-md font-medium text-foreground">Study Materials</h4>
      <div className="space-y-3">
        {fields.map((materialField, materialIndex) => {
           const materialType = watch(`units.${unitIndex}.materials.${materialIndex}.type`);
           return(
            <div key={materialField.id} className="p-3 border rounded-md relative space-y-2 bg-muted/50">
                <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute top-1 right-1 h-7 w-7 text-destructive hover:bg-destructive/10"
                    onClick={() => remove(materialIndex)}
                >
                    <Trash2 className="h-4 w-4" />
                </Button>
                <FormField
                    control={control}
                    name={`units.${unitIndex}.materials.${materialIndex}.name`}
                    render={({ field }) => (
                    <FormItem>
                        <FormLabel className="text-xs">Material Name</FormLabel>
                        <FormControl><Input {...field} placeholder="e.g., Syllabus PDF" /></FormControl>
                        <FormMessage />
                    </FormItem>
                    )}
                />
                <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                    <FormField
                    control={control}
                    name={`units.${unitIndex}.materials.${materialIndex}.type`}
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel className="text-xs">Type</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                <SelectTrigger><SelectValue placeholder="Select type" /></SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                {materialTypes.map(type => (
                                    <SelectItem key={type} value={type}>{type}</SelectItem>
                                ))}
                                </SelectContent>
                            </Select>
                            <FormMessage />
                        </FormItem>
                    )}
                    />
                    {materialType === 'Text' ? (
                    <FormField
                        control={control}
                        name={`units.${unitIndex}.materials.${materialIndex}.content`}
                        render={({ field }) => (
                        <FormItem className="md:col-span-2">
                            <FormLabel className="text-xs">Content</FormLabel>
                            <FormControl><Textarea {...field} placeholder="Enter text content..." rows={2} /></FormControl>
                            <FormMessage />
                        </FormItem>
                        )}
                    />
                    ) : (
                    <FormField
                        control={control}
                        name={`units.${unitIndex}.materials.${materialIndex}.url`}
                        render={({ field }) => (
                        <FormItem className="md:col-span-2">
                            <FormLabel className="text-xs">URL</FormLabel>
                            <FormControl><Input {...field} placeholder="https://..." /></FormControl>
                            <FormMessage />
                        </FormItem>
                        )}
                    />
                    )}
                </div>
            </div>
           )
        })}
      </div>

      <Button
          type="button"
          variant="secondary"
          size="sm"
          className="mt-2"
          onClick={() => append({ name: '', type: 'Link', url: '', content: '' })}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Add Material to this Unit
        </Button>
    </div>
  );
}
