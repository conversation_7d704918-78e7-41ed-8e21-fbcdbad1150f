
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { format } from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import type { Course, EducationDetails } from '@/types';
import { Send, Loader2 } from 'lucide-react';
import { useState, useMemo } from 'react';
import { auth } from '@/lib/firebase';
import { createUserWithEmailAndPassword, updateProfile, signOut } from 'firebase/auth';
import { createApplication, type CreateApplicationData } from '@/actions/applicationActions';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

const educationLevels = [
  { id: '10th', label: '10th Grade', yearFieldName: 'tenthCompletionYear' as const },
  { id: '12th', label: '12th Grade', yearFieldName: 'twelfthCompletionYear' as const },
  { id: 'diploma', label: 'Diploma', yearFieldName: 'diplomaCompletionYear' as const },
  { id: 'graduate', label: "Bachelor's Degree", yearFieldName: 'graduateCompletionYear' as const },
  { id: 'masters', label: "Master's Degree", yearFieldName: 'mastersCompletionYear' as const },
] as const;


const currentYear = new Date().getFullYear();
const minBirthYear = currentYear - 100;
const minCompletionYear = currentYear - 50;


const applicationFormSchema = z.object({
  fullName: z.string().min(2, { message: 'Full name must be at least 2 characters.' }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  mobileNumber: z.string().regex(/^[6-9]\d{9}$/, { message: 'Please enter a valid 10-digit Indian mobile number starting with 6, 7, 8, or 9.' }),
  motherName: z.string().min(2, { message: "Mother's name must be at least 2 characters." }),
  fatherName: z.string().min(2, { message: "Father's name must be at least 2 characters." }),
  dob_day: z.string().min(1, { message: 'Day is required.' }),
  dob_month: z.string().min(1, { message: 'Month is required.' }),
  dob_year: z.string().min(1, { message: 'Year is required.' }),
  religion: z.string().min(2, { message: "Religion is required." }),
  desiredCourse: z.string({ required_error: 'Please select a course.' }).min(1, { message: 'Please select a course.' }),
  previousEducation: z.array(z.string()).nonempty({ message: 'Please select at least one qualification.' }),
  reference: z.string().min(1, { message: "Reference is required." }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters.'}).optional(),
  confirmPassword: z.string().optional(),
  educationDetails: z.object({
    tenthCompletionYear: z.string().optional(),
    twelfthCompletionYear: z.string().optional(),
    diplomaCompletionYear: z.string().optional(),
    graduateCompletionYear: z.string().optional(),
    mastersCompletionYear: z.string().optional(),
  }).optional(),
}).refine((data) => {
    if(data.password) {
        return data.password === data.confirmPassword;
    }
    return true;
}, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
}).superRefine((data, ctx) => {
  // --- Date of Birth validation ---
  if (data.dob_day && data.dob_month && data.dob_year) {
    const day = parseInt(data.dob_day, 10);
    const month = parseInt(data.dob_month, 10);
    const year = parseInt(data.dob_year, 10);
    const date = new Date(year, month - 1, day);
    
    if (isNaN(date.getTime()) || date.getFullYear() !== year || date.getMonth() + 1 !== month || date.getDate() !== day) {
      ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Invalid date.", path: ["dob_day"] });
    } else if (year > currentYear || year < minBirthYear) {
       ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Year must be after ${minBirthYear}.`,
        path: ["dob_year"],
      });
    }
  }

  // --- Conditional Education Details validation ---
  const selectedEducation = data.previousEducation || [];
  const educationDetails = data.educationDetails || {};

  educationLevels.forEach(level => {
      if (selectedEducation.includes(level.id)) {
          const yearValue = educationDetails[level.yearFieldName];
          const fieldPath = `educationDetails.${level.yearFieldName}`;

          if (!yearValue || yearValue.trim() === '') {
              ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: `Year is required.`,
                  path: [fieldPath],
              });
          } else if (!/^\d{4}$/.test(yearValue)) {
              ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: "Must be a 4-digit year.",
                  path: [fieldPath],
              });
          } else {
              const numericYear = parseInt(yearValue, 10);
              if (numericYear > currentYear || numericYear < minCompletionYear) {
                  ctx.addIssue({
                      code: z.ZodIssueCode.custom,
                      message: `Year must be between ${minCompletionYear} and ${currentYear}.`,
                      path: [fieldPath],
                  });
              }
          }
      }
  });
});


type ApplicationFormValues = z.infer<typeof applicationFormSchema>;

export function ApplicationForm({ courses: availableCourses }: { courses: Course[] }) {
  const { user } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ApplicationFormValues>({
    resolver: zodResolver(applicationFormSchema),
    defaultValues: {
      fullName: user?.name || '',
      email: user?.email || '',
      mobileNumber: '',
      motherName: '',
      fatherName: '',
      dob_day: '',
      dob_month: '',
      dob_year: '',
      religion: '',
      previousEducation: [],
      reference: '',
      password: '',
      confirmPassword: '',
      educationDetails: {
        tenthCompletionYear: '',
        twelfthCompletionYear: '',
        diplomaCompletionYear: '',
        graduateCompletionYear: '',
        mastersCompletionYear: '',
      },
    },
    mode: 'onBlur',
  });

  const watchedPreviousEducation = form.watch('previousEducation') || [];
  
  const groupedCourses = useMemo(() => {
    return availableCourses.reduce<Record<string, Course[]>>((acc, course) => {
      const groupName = `${course.name} (${course.code})`;
      if (!acc[groupName]) {
        acc[groupName] = [];
      }
      acc[groupName].push(course);
      return acc;
    }, {});
  }, [availableCourses]);

  async function onSubmit(data: ApplicationFormValues) {
    if (isSubmitting) {
      return;
    }
    setIsSubmitting(true);

    let userId = user?.uid;
    let createdUser = false;

    try {
      if (!userId) {
        if (!data.password) {
            toast({
                title: 'Password Required',
                description: 'Please provide a password to create your account.',
                variant: 'destructive',
            });
            setIsSubmitting(false);
            return;
        }
        const userCredential = await createUserWithEmailAndPassword(auth, data.email, data.password);
        await updateProfile(userCredential.user, { displayName: data.fullName });
        userId = userCredential.user.uid;
        createdUser = true;
      } else {
        if (auth.currentUser && auth.currentUser.displayName !== data.fullName) {
          await updateProfile(auth.currentUser, { displayName: data.fullName });
        }
      }

      const dateOfBirthString = format(new Date(parseInt(data.dob_year), parseInt(data.dob_month) - 1, parseInt(data.dob_day)), 'yyyy-MM-dd');

      const selectedCourse = availableCourses.find(c => c.id === data.desiredCourse);
      const specialization = selectedCourse?.specializations?.[0];

      const applicationPayload: CreateApplicationData = {
        fullName: data.fullName,
        email: data.email,
        mobileNumber: data.mobileNumber,
        motherName: data.motherName,
        fatherName: data.fatherName,
        dateOfBirth: dateOfBirthString,
        religion: data.religion,
        desiredCourse: data.desiredCourse,
        specialization,
        previousEducation: data.previousEducation,
        reference: data.reference,
        userId: userId,
        educationDetails: data.educationDetails,
      };

      await createApplication(applicationPayload);

      if (createdUser) {
        await signOut(auth);
      }
      
      router.push('/apply/success');

    } catch (error: any) {
      console.error("Error during application submission or user creation:", error);
      let errorMessage = 'An unexpected error occurred. Please try again.';
      if (error.code) {
        switch (error.code) {
          case 'auth/email-already-in-use':
            errorMessage = 'This email address is already in use. Please try logging in or use a different email.';
            break;
          case 'auth/weak-password':
            errorMessage = 'The password is too weak. Please choose a stronger password.';
            break;
          default:
            errorMessage = `Registration failed: ${error.message}`;
        }
      }
      toast({
        title: 'Submission Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  }
  
  const years = Array.from({ length: currentYear - minBirthYear + 1 }, (_, i) => currentYear - i);
  const completionYears = Array.from({ length: currentYear - minCompletionYear + 1 }, (_, i) => currentYear - i);
  const months = [
    { value: '1', label: 'January' }, { value: '2', label: 'February' },
    { value: '3', label: 'March' }, { value: '4', label: 'April' },
    { value: '5', label: 'May' }, { value: '6', label: 'June' },
    { value: '7', label: 'July' }, { value: '8', label: 'August' },
    { value: '9', label: 'September' }, { value: '10', label: 'October' },
    { value: '11', label: 'November' }, { value: '12', label: 'December' },
  ];
  const days = Array.from({ length: 31 }, (_, i) => i + 1);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full Name</FormLabel>
              <FormControl>
                <Input placeholder="John Doe" {...field} disabled={isSubmitting} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <FormField
            control={form.control}
            name="motherName"
            render={({ field }) => (
                <FormItem>
                <FormLabel>Mother's Name</FormLabel>
                <FormControl>
                    <Input placeholder="Jane Doe" {...field} disabled={isSubmitting} />
                </FormControl>
                <FormMessage />
                </FormItem>
            )}
            />
            <FormField
            control={form.control}
            name="fatherName"
            render={({ field }) => (
                <FormItem>
                <FormLabel>Father's Name</FormLabel>
                <FormControl>
                    <Input placeholder="Richard Doe" {...field} disabled={isSubmitting} />
                </FormControl>
                <FormMessage />
                </FormItem>
            )}
            />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <FormItem>
              <FormLabel>Date of Birth</FormLabel>
              <div className="grid grid-cols-3 gap-2">
                <FormField
                  control={form.control}
                  name="dob_day"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <Select onValueChange={field.onChange} value={field.value} disabled={isSubmitting}>
                        <FormControl>
                          <SelectTrigger><SelectValue placeholder="Day" /></SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {days.map(day => <SelectItem key={day} value={String(day)}>{day}</SelectItem>)}
                        </SelectContent>
                      </Select>
                      <FormMessage className="pt-1" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="dob_month"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <Select onValueChange={field.onChange} value={field.value} disabled={isSubmitting}>
                        <FormControl>
                          <SelectTrigger><SelectValue placeholder="Month" /></SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {months.map(month => <SelectItem key={month.value} value={month.value}>{month.label}</SelectItem>)}
                        </SelectContent>
                      </Select>
                       <FormMessage className="pt-1" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="dob_year"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <Select onValueChange={field.onChange} value={field.value} disabled={isSubmitting}>
                        <FormControl>
                          <SelectTrigger><SelectValue placeholder="Year" /></SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {years.map(year => <SelectItem key={year} value={String(year)}>{year}</SelectItem>)}
                        </SelectContent>
                      </Select>
                       <FormMessage className="pt-1" />
                    </FormItem>
                  )}
                />
              </div>
            </FormItem>
           <FormField
            control={form.control}
            name="religion"
            render={({ field }) => (
                <FormItem>
                <FormLabel>Religion</FormLabel>
                <FormControl>
                    <Input placeholder="e.g., Hinduism, Christianity" {...field} disabled={isSubmitting} />
                </FormControl>
                <FormMessage />
                </FormItem>
            )}
            />
        </div>


        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Address</FormLabel>
                <FormControl>
                  <Input type="email" placeholder="<EMAIL>" {...field} disabled={isSubmitting || !!user} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="mobileNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Mobile Number</FormLabel>
                <FormControl>
                  <Input type="tel" placeholder="9876543210" {...field} disabled={isSubmitting} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="desiredCourse"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Desired Course</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}
                disabled={isSubmitting}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={"Select a course"} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.keys(groupedCourses).length > 0 ? (
                    Object.entries(groupedCourses).map(([groupName, coursesInGroup]) => {
                      if (coursesInGroup.length === 1) {
                        const course = coursesInGroup[0];
                        return (
                          <SelectItem key={course.id} value={course.id}>
                            {groupName}
                          </SelectItem>
                        );
                      } else {
                        return (
                          <SelectGroup key={groupName}>
                            <SelectLabel>{groupName}</SelectLabel>
                            {coursesInGroup.map(course => (
                              <SelectItem key={course.id} value={course.id}>
                                {course.specializations?.[0] || 'General'}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        );
                      }
                    })
                  ) : (
                     <SelectItem value="no-courses" disabled>No courses available</SelectItem>
                  )}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormItem>
            <div className="mb-4">
            <FormLabel className="text-base">Previous Education / Qualifications</FormLabel>
            <FormDescription>
                Select all that apply. Completion year will be required for selected items.
            </FormDescription>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2">
            <FormField
                control={form.control}
                name="previousEducation"
                render={() => (
                    <div>
                        {educationLevels.map((item) => (
                            <FormField
                                key={item.id}
                                control={form.control}
                                name="previousEducation"
                                render={({ field }) => {
                                    return (
                                    <FormItem
                                        key={item.id}
                                        className="flex flex-row items-center space-x-3 space-y-0"
                                    >
                                        <FormControl>
                                        <Checkbox
                                            checked={field.value?.includes(item.id)}
                                            onCheckedChange={(checked) => {
                                                const newValue = checked
                                                ? [...(field.value || []), item.id]
                                                : field.value?.filter(
                                                    (value) => value !== item.id
                                                );
                                                field.onChange(newValue);
                                                // Trigger validation for the related year field when checkbox changes
                                                if (!checked && form.formState.errors.educationDetails?.[item.yearFieldName]) {
                                                   form.clearErrors(`educationDetails.${item.yearFieldName}`);
                                                }
                                            }}
                                        />
                                        </FormControl>
                                        <FormLabel className="font-normal">
                                            {item.label}
                                        </FormLabel>
                                    </FormItem>
                                    );
                                }}
                            />
                        ))}
                    </div>
                )}
            />
            </div>
            <FormMessage>{form.formState.errors.previousEducation?.message}</FormMessage>
        </FormItem>

        
        {watchedPreviousEducation.length > 0 && (
          <div className="space-y-4 rounded-md border p-4">
            <h4 className="text-sm font-medium text-foreground">Please provide completion years:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {educationLevels.map((level) => {
                if (watchedPreviousEducation.includes(level.id)) {
                  return (
                    <FormField
                      key={`${level.id}-year-input`}
                      control={form.control}
                      name={`educationDetails.${level.yearFieldName}`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{level.label} Year</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="YYYY"
                              {...field}
                              disabled={isSubmitting}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  );
                }
                return null;
              })}
            </div>
          </div>
        )}

        <FormField
          control={form.control}
          name="reference"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Reference</FormLabel>
              <Select onValueChange={field.onChange} value={field.value} disabled={isSubmitting}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="How did you hear about us?" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="Friend">Friend / Referral</SelectItem>
                  <SelectItem value="Social Media">Social Media</SelectItem>
                  <SelectItem value="Online Ad">Online Advertisement</SelectItem>
                  <SelectItem value="Search Engine">Search Engine (Google, etc.)</SelectItem>
                  <SelectItem value="Event">Educational Event</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {!user && (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                    <FormItem>
                    <FormLabel>Create a Password</FormLabel>
                    <FormControl>
                        <Input type="password" placeholder="••••••••" {...field} disabled={isSubmitting} />
                    </FormControl>
                    <FormMessage />
                    </FormItem>
                )}
                />
                <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                    <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                        <Input type="password" placeholder="••••••••" {...field} disabled={isSubmitting} />
                    </FormControl>
                    <FormMessage />
                    </FormItem>
                )}
                />
            </div>
        )}
        

        <Button type="submit" className="w-full bg-accent hover:bg-accent/90 text-accent-foreground" disabled={isSubmitting || availableCourses.length === 0}>
          {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Send className="mr-2 h-4 w-4" />}
          {user ? 'Submit Application' : 'Create Account & Submit Application'}
        </Button>
      </form>
    </Form>
  );
}
