'use server';

import { adminAuth } from '@/lib/firebase-admin';
import { requireAuth } from '@/lib/authUtils';

/**
 * Revokes the refresh tokens for all users in Firebase Authentication.
 * This effectively forces all users to log in again.
 * This is a sensitive operation and should only be exposed to system administrators.
 * @param idToken The Firebase ID token of the calling admin user.
 * @returns An object with the total number of users whose sessions were revoked.
 */
export async function forceLogoutAllUsers(idToken: string): Promise<{ revokedUsersCount: number }> {
  // Ensure only an admin can perform this action
  await requireAuth(idToken, ['admin']);

  if (!adminAuth) {
    throw new Error('Admin SDK not initialized. Check server logs.');
  }

  let pageToken: string | undefined;
  let revokedUsersCount = 0;

  try {
    do {
      const listUsersResult = await adminAuth.listUsers(1000, pageToken);
      const uidsToRevoke = listUsersResult.users.map((userRecord) => userRecord.uid);
      
      if (uidsToRevoke.length > 0) {
        // Revoke tokens for the current batch of users
        const revokePromises = uidsToRevoke.map(uid => 
          adminAuth.revokeRefreshTokens(uid)
            .then(() => {
              revokedUsersCount++;
            })
            .catch(error => {
              console.error(`Failed to revoke tokens for user: ${uid}`, error);
              // Continue even if one fails
            })
        );
        await Promise.all(revokePromises);
      }
      
      pageToken = listUsersResult.pageToken;
    } while (pageToken);


    return { revokedUsersCount };

  } catch (error) {
    console.error('Error while forcing logout for all users:', error);
    throw new Error('An unexpected error occurred while revoking user sessions.');
  }
}

/**
 * Sets custom claims (role) for a Firebase user.
 * This allows you to assign admin, accountant, or student roles to users.
 * @param idToken The Firebase ID token of the calling admin user.
 * @param targetUserEmail The email of the user to assign the role to.
 * @param role The role to assign ('admin', 'accountant', or 'student').
 * @returns An object indicating success or failure.
 */
export async function setUserRole(
  idToken: string,
  targetUserEmail: string,
  role: 'admin' | 'accountant' | 'student'
): Promise<{ success: boolean; message: string }> {
  // Ensure only an admin can perform this action
  await requireAuth(idToken, ['admin']);

  if (!adminAuth) {
    throw new Error('Admin SDK not initialized. Check server logs.');
  }

  try {
    // Get user by email
    const userRecord = await adminAuth.getUserByEmail(targetUserEmail);

    // Set custom claims
    await adminAuth.setCustomUserClaims(userRecord.uid, { role });

    // Revoke existing tokens to force the user to get new tokens with updated claims
    await adminAuth.revokeRefreshTokens(userRecord.uid);

    return {
      success: true,
      message: `Successfully assigned '${role}' role to ${targetUserEmail}. User will need to sign out and sign back in for changes to take effect.`
    };
  } catch (error: any) {
    console.error('Error setting user role:', error);

    if (error.code === 'auth/user-not-found') {
      return {
        success: false,
        message: `User with email ${targetUserEmail} not found. Please ensure the user has created an account first.`
      };
    }

    return {
      success: false,
      message: `Failed to set user role: ${error.message}`
    };
  }
}

/**
 * Lists all users with their roles for admin management.
 * @param idToken The Firebase ID token of the calling admin user.
 * @returns An array of users with their roles.
 */
export async function listUsersWithRoles(idToken: string): Promise<Array<{
  uid: string;
  email: string;
  displayName: string;
  role: string;
  disabled: boolean;
  creationTime: string;
  lastSignInTime: string;
}>> {
  // Ensure only an admin can perform this action
  await requireAuth(idToken, ['admin']);

  if (!adminAuth) {
    throw new Error('Admin SDK not initialized. Check server logs.');
  }

  try {
    const listUsersResult = await adminAuth.listUsers(1000);

    return listUsersResult.users.map(userRecord => ({
      uid: userRecord.uid,
      email: userRecord.email || 'No email',
      displayName: userRecord.displayName || 'No name',
      role: userRecord.customClaims?.role || 'student',
      disabled: userRecord.disabled,
      creationTime: userRecord.metadata.creationTime,
      lastSignInTime: userRecord.metadata.lastSignInTime || 'Never'
    }));
  } catch (error: any) {
    console.error('Error listing users:', error);
    throw new Error(`Failed to list users: ${error.message}`);
  }
}
