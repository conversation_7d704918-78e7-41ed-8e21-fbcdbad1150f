globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(app)/applications/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/layout/ThemeProvider.tsx":{"*":{"id":"(ssr)/./src/components/layout/ThemeProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/toaster.tsx":{"*":{"id":"(ssr)/./src/components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/layout.tsx":{"*":{"id":"(ssr)/./src/app/(app)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/(app)/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/applications/page.tsx":{"*":{"id":"(ssr)/./src/app/(app)/applications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/students/page.tsx":{"*":{"id":"(ssr)/./src/app/(app)/students/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/fee-management/page.tsx":{"*":{"id":"(ssr)/./src/app/(app)/fee-management/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx":{"*":{"id":"(ssr)/./src/app/(app)/financial-reports/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/courses/CourseList.tsx":{"*":{"id":"(ssr)/./src/components/courses/CourseList.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/settings/page.tsx":{"*":{"id":"(ssr)/./src/app/(app)/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/my-courses/page.tsx":{"*":{"id":"(ssr)/./src/app/(app)/my-courses/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/my-fees/page.tsx":{"*":{"id":"(ssr)/./src/app/(app)/my-fees/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/invoice/[feeId]/page.tsx":{"*":{"id":"(ssr)/./src/app/(app)/invoice/[feeId]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Code\\edulite\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Code\\edulite\\src\\components\\layout\\ThemeProvider.tsx":{"id":"(app-pages-browser)/./src/components/layout/ThemeProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Code\\edulite\\src\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./src/components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Code\\edulite\\src\\contexts\\AuthContext.tsx":{"id":"(app-pages-browser)/./src/contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Code\\edulite\\src\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Code\\edulite\\src\\app\\(app)\\layout.tsx":{"id":"(app-pages-browser)/./src/app/(app)/layout.tsx","name":"*","chunks":["app/(app)/layout","static/chunks/app/(app)/layout.js"],"async":false},"C:\\Code\\edulite\\src\\app\\(app)\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/(app)/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Code\\edulite\\src\\app\\(app)\\applications\\page.tsx":{"id":"(app-pages-browser)/./src/app/(app)/applications/page.tsx","name":"*","chunks":["app/(app)/applications/page","static/chunks/app/(app)/applications/page.js"],"async":false},"C:\\Code\\edulite\\src\\app\\(app)\\students\\page.tsx":{"id":"(app-pages-browser)/./src/app/(app)/students/page.tsx","name":"*","chunks":[],"async":false},"C:\\Code\\edulite\\src\\app\\(app)\\fee-management\\page.tsx":{"id":"(app-pages-browser)/./src/app/(app)/fee-management/page.tsx","name":"*","chunks":[],"async":false},"C:\\Code\\edulite\\src\\app\\(app)\\financial-reports\\page.tsx":{"id":"(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx","name":"*","chunks":[],"async":false},"C:\\Code\\edulite\\src\\components\\courses\\CourseList.tsx":{"id":"(app-pages-browser)/./src/components/courses/CourseList.tsx","name":"*","chunks":[],"async":false},"C:\\Code\\edulite\\src\\app\\(app)\\settings\\page.tsx":{"id":"(app-pages-browser)/./src/app/(app)/settings/page.tsx","name":"*","chunks":[],"async":false},"C:\\Code\\edulite\\src\\app\\(app)\\my-courses\\page.tsx":{"id":"(app-pages-browser)/./src/app/(app)/my-courses/page.tsx","name":"*","chunks":[],"async":false},"C:\\Code\\edulite\\src\\app\\(app)\\my-fees\\page.tsx":{"id":"(app-pages-browser)/./src/app/(app)/my-fees/page.tsx","name":"*","chunks":[],"async":false},"C:\\Code\\edulite\\src\\app\\(app)\\invoice\\[feeId]\\page.tsx":{"id":"(app-pages-browser)/./src/app/(app)/invoice/[feeId]/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Code\\edulite\\src\\":[],"C:\\Code\\edulite\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Code\\edulite\\src\\app\\(app)\\layout":[],"C:\\Code\\edulite\\src\\app\\(app)\\applications\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/ThemeProvider.tsx":{"*":{"id":"(rsc)/./src/components/layout/ThemeProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/toaster.tsx":{"*":{"id":"(rsc)/./src/components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(rsc)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(rsc)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/layout.tsx":{"*":{"id":"(rsc)/./src/app/(app)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/(app)/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/applications/page.tsx":{"*":{"id":"(rsc)/./src/app/(app)/applications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/students/page.tsx":{"*":{"id":"(rsc)/./src/app/(app)/students/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/fee-management/page.tsx":{"*":{"id":"(rsc)/./src/app/(app)/fee-management/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx":{"*":{"id":"(rsc)/./src/app/(app)/financial-reports/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/courses/CourseList.tsx":{"*":{"id":"(rsc)/./src/components/courses/CourseList.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/settings/page.tsx":{"*":{"id":"(rsc)/./src/app/(app)/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/my-courses/page.tsx":{"*":{"id":"(rsc)/./src/app/(app)/my-courses/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/my-fees/page.tsx":{"*":{"id":"(rsc)/./src/app/(app)/my-fees/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/invoice/[feeId]/page.tsx":{"*":{"id":"(rsc)/./src/app/(app)/invoice/[feeId]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}