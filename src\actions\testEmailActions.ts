'use server';

import { sendEmail } from '@/lib/email';
import { requireAuth } from '@/lib/authUtils';
import React from 'react';

/**
 * Test email functionality - sends a test email to verify Resend configuration
 * Only admins can use this function
 */
export async function sendTestEmail(idToken: string, testEmailAddress: string): Promise<{ success: boolean; message: string }> {
  await requireAuth(idToken, ['admin']);

  if (!testEmailAddress || !testEmailAddress.includes('@')) {
    return { success: false, message: 'Please provide a valid email address.' };
  }

  try {
    const result = await sendEmail({
      to: testEmailAddress,
      subject: 'EduLite Email Test - Configuration Verification',
      reactElement: React.createElement('div', {}, [
        React.createElement('h1', { key: 'title' }, 'EduLite Email Test'),
        React.createElement('p', { key: 'message' }, 'This is a test email to verify that your Resend email configuration is working correctly.'),
        React.createElement('p', { key: 'details' }, 'If you received this email, your email system is properly configured!'),
        React.createElement('hr', { key: 'separator' }),
        React.createElement('p', { key: 'footer', style: { fontSize: '12px', color: '#666' } }, 'Sent from EduLite Student Management System')
      ])
    });

    if (result.success) {
      return { 
        success: true, 
        message: `Test email sent successfully to ${testEmailAddress}. Check your inbox!` 
      };
    } else {
      return { 
        success: false, 
        message: `Failed to send test email: ${result.error}` 
      };
    }
  } catch (error: any) {
    console.error('Error in sendTestEmail:', error);
    return { 
      success: false, 
      message: `Error sending test email: ${error.message || 'Unknown error'}` 
    };
  }
}
