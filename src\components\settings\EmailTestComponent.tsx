'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, Mail, CheckCircle, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { sendTestEmail } from '@/actions/testEmailActions';
import { auth } from '@/lib/firebase';

export function EmailTestComponent() {
  const [testEmail, setTestEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);
  const { toast } = useToast();

  const handleSendTestEmail = async () => {
    if (!auth.currentUser || !testEmail.trim()) return;
    
    setIsLoading(true);
    setResult(null);
    
    try {
      const idToken = await auth.currentUser.getIdToken();
      const response = await sendTestEmail(idToken, testEmail.trim());
      setResult(response);
      
      if (response.success) {
        toast({
          title: "Test Email Sent",
          description: response.message,
        });
      } else {
        toast({
          title: "Email Test Failed",
          description: response.message,
          variant: "destructive"
        });
      }
    } catch (error: any) {
      const errorMessage = error.message || 'An unknown error occurred.';
      setResult({ success: false, message: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <h4 className="font-semibold">Test Email Configuration</h4>
        <p className="text-sm text-muted-foreground">Send a test email to verify your Resend configuration is working.</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-2 space-y-2">
          <Label htmlFor="test-email">Test Email Address</Label>
          <Input
            id="test-email"
            type="email"
            placeholder="<EMAIL>"
            value={testEmail}
            onChange={(e) => setTestEmail(e.target.value)}
            disabled={isLoading}
          />
        </div>
        <div className="flex items-end">
          <Button 
            onClick={handleSendTestEmail} 
            disabled={isLoading || !testEmail.trim()}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Mail className="mr-2 h-4 w-4" />
                Send Test Email
              </>
            )}
          </Button>
        </div>
      </div>
      
      {result && (
        <Alert variant={result.success ? 'default' : 'destructive'}>
          {result.success ? <CheckCircle className="h-4 w-4" /> : <AlertTriangle className="h-4 w-4" />}
          <AlertTitle>{result.success ? 'Success' : 'Error'}</AlertTitle>
          <AlertDescription className="whitespace-pre-wrap text-sm">
            {result.message}
          </AlertDescription>
        </Alert>
      )}
      
      <div className="text-xs text-muted-foreground">
        <p><strong>Note:</strong> Make sure your domain (edulite.co) is verified in your Resend dashboard.</p>
        <p>If emails are not being delivered, check your Resend logs and domain verification status.</p>
      </div>
    </div>
  );
}
