
'use server';

import { db } from '@/lib/firebase-admin';
import { requireAuth } from '@/lib/authUtils';

/**
 * Performs a simple CRUD test on Firestore to verify the connection and permissions.
 * This is a diagnostic tool for administrators.
 * @param idToken The Firebase ID token of the calling admin user.
 * @returns An object indicating success or failure.
 */
export async function runFirestoreHealthCheck(idToken: string): Promise<{ success: boolean; message: string }> {
  await requireAuth(idToken, ['admin']);

  if (!db) {
    throw new Error('Database not initialized. Check server logs.');
  }

  const testCollection = '_healthChecks';
  const testDocId = `check-${Date.now()}`;
  const docRef = db.collection(testCollection).doc(testDocId);

  try {
    // 1. Create
    await docRef.set({ status: 'created', timestamp: new Date() });

    // 2. Read
    const doc = await docRef.get();
    if (!doc.exists || doc.data()?.status !== 'created') {
      throw new Error('Read verification failed.');
    }

    // 3. Update
    await docRef.update({ status: 'updated' });
    const updatedDoc = await docRef.get();
    if (updatedDoc.data()?.status !== 'updated') {
        throw new Error('Update verification failed.');
    }

    // 4. Delete
    await docRef.delete();
    const finalDoc = await docRef.get();
    if (finalDoc.exists) {
        throw new Error('Delete verification failed.');
    }
    return { success: true, message: 'Firestore connection is healthy. All CRUD operations were successful.' };

  } catch (error: any) {
    console.error('Firestore Health Check FAILED:', error);
    // Attempt to clean up if the test failed mid-way
    try {
        await docRef.delete();
    } catch (cleanupError) {
        // Ignore cleanup errors
    }
    
    let friendlyMessage = `Firestore health check failed: ${error.message}`;
    if (error.code === 7) { // Firestore PERMISSION_DENIED error code
      friendlyMessage = "Firestore health check failed with 'PERMISSION_DENIED'.\n\n" +
                        "This almost always means the service account key (`service-account.json`) does not have the correct IAM permissions in your Google Cloud project. Please ensure it has the 'Cloud Datastore User' or 'Editor' role.";
    } else if (error.code === 5) { // Firestore NOT_FOUND, usually means DB not created
      friendlyMessage = "Firestore health check failed with 'NOT_FOUND'. This error has two common causes:\n\n" +
                        "1. **Firestore Database Not Created:** Please go to the Firebase Console, select your project, find 'Firestore Database' in the Build menu, and click 'Create database'. This is a required one-time setup step.\n\n" +
                        "2. **Incorrect Permissions:** If the database is already created, ensure the service account used by the server has the 'Cloud Datastore User' or 'Editor' role in your Google Cloud project's IAM settings.";
    }


    return { success: false, message: friendlyMessage };
  }
}


/**
 * Verifies that the current user's token has the 'admin' role claim.
 * @param idToken The Firebase ID token of the calling admin user.
 * @returns An object indicating success or failure.
 */
export async function verifyAdminStatus(idToken: string): Promise<{ success: boolean; message: string }> {
  try {
    await requireAuth(idToken, ['admin']);
    return { success: true, message: 'Admin role confirmed. You have full administrative privileges.' };
  } catch (error: any) {
    // requireAuth throws a user-friendly error, so we can pass it along.
    return { success: false, message: `Verification failed: ${error.message}` };
  }
}
