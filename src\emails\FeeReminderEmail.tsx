
import React from 'react';
import {
  <PERSON>,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Text,
  Section,
} from '@react-email/components';

interface FeeReminderEmailProps {
  studentName: string;
  courseName: string;
  feeDescription: string;
  amountDue: number;
  dueDate: string;
  invoiceUrl: string;
}

const FeeReminderEmail: React.FC<FeeReminderEmailProps> = ({
  studentName,
  courseName,
  feeDescription,
  amountDue,
  dueDate,
  invoiceUrl,
}) => {
  return (
    <Html>
      <Head />
      <Preview>Fee Payment Reminder</Preview>
      <Body style={main}>
        <Container style={container}>
          <Heading style={heading}>Fee Payment Reminder</Heading>
          <Text style={paragraph}>Dear {studentName},</Text>
          <Text style={paragraph}>
            This is a friendly reminder about your fee payment for <strong>{courseName}</strong>. Please review the details below and make your payment at your earliest convenience.
          </Text>
          <Section style={detailsSection}>
            <Text style={detailText}><strong>Fee Item:</strong> {feeDescription}</Text>
            <Text style={detailText}><strong>Outstanding Amount:</strong> ₹{amountDue.toFixed(2)}</Text>
            <Text style={detailText}><strong>Original Due Date:</strong> {new Date(dueDate).toLocaleDateString()}</Text>
          </Section>
          <Text style={paragraph}>
            Please settle the outstanding amount to ensure your continued enrollment and access to course materials.
          </Text>
          <Button
            style={button}
            href={invoiceUrl}
          >
            View Receipt and Pay Now
          </Button>
          <Text style={footer}>
            If you have already made this payment, please disregard this email. It may take a few business days for the payment to be reflected in our system.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default FeeReminderEmail;

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
  border: '1px solid #f0f0f0',
  borderRadius: '4px',
};

const heading = {
  fontSize: '28px',
  fontWeight: 'bold',
  textAlign: 'center' as const,
  color: '#e53e3e', // Destructive color for urgency
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '24px',
  color: '#525f7f',
  padding: '0 40px',
};

const detailsSection = {
  padding: '20px',
  margin: '20px 40px',
  backgroundColor: '#fff5f5',
  border: '1px solid #e53e3e',
  borderRadius: '4px',
};

const detailText = {
  fontSize: '14px',
  lineHeight: '1.5',
  color: '#333',
  margin: '0 0 10px 0',
};

const button = {
  backgroundColor: '#009688',
  borderRadius: '5px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '220px',
  padding: '12px',
  margin: '20px auto',
};

const footer = {
  color: '#8898aa',
  fontSize: '12px',
  lineHeight: '16px',
  textAlign: 'center' as const,
  marginTop: '20px',
  padding: '0 40px',
};
