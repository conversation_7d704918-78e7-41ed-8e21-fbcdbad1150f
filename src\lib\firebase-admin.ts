import admin from 'firebase-admin';
import { getFirestore } from 'firebase-admin/firestore';

let serviceAccount: admin.ServiceAccount | null = null;

// Use environment variables for Firebase Admin SDK configuration
if (process.env.FIREBASE_PRIVATE_KEY && process.env.FIREBASE_CLIENT_EMAIL && process.env.FIREBASE_PROJECT_ID) {
  serviceAccount = {
    projectId: process.env.FIREBASE_PROJECT_ID,
    privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
  };
} else {
  console.warn(
    'Warning: Firebase service account credentials not found. This is expected during build time. Please set environment variables for runtime: FIREBASE_PROJECT_ID, FIREBASE_PRIVATE_KEY, FIREBASE_CLIENT_EMAIL'
  );
}

// Only initialize Firebase Admin if we have credentials
if (serviceAccount && !admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

// Export functions that handle missing initialization gracefully
export const adminAuth = serviceAccount ? admin.auth() : null;
export const db = serviceAccount ? getFirestore() : null;
