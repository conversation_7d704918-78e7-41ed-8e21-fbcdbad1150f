import admin from 'firebase-admin';
import { getFirestore } from 'firebase-admin/firestore';

let serviceAccount: admin.ServiceAccount;

try {
  // Try to load service account from file
  serviceAccount = require('../../service-account.json');
} catch (e) {
  // If file doesn't exist, try to use environment variables
  if (process.env.FIREBASE_PRIVATE_KEY && process.env.FIREBASE_CLIENT_EMAIL && process.env.FIREBASE_PROJECT_ID) {
    serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_PROJECT_ID,
      private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
    };
  } else {
    console.error(
      'Error: Firebase service account credentials not found. Either place service-account.json at the project root or set environment variables: FIREBASE_PROJECT_ID, FIREBASE_PRIVATE_KEY, FIREBASE_CLIENT_EMAIL'
    );
    throw new Error(
      'Service account initialization failed. See server logs for details.'
    );
  }
}

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

export const adminAuth = admin.auth();
export const db = getFirestore();
