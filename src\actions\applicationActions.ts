
'use server';

import { revalidatePath } from 'next/cache';
import { db } from '@/lib/firebase-admin';
import { FieldValue } from 'firebase-admin/firestore';
import type { StudentApplication, EducationDetails, User } from '@/types';
import { getCourseById } from './courseActions';
import { getCourseFeeStructureByCourseId, createStudentFee, type CreateStudentFeeData } from './feeActions';
import { format, addDays } from 'date-fns';
import { requireAuth } from '@/lib/authUtils';
import React from 'react';
import { sendEmail } from '@/lib/email';
import ApplicationSubmittedEmail from '@/emails/ApplicationSubmittedEmail';
import ApplicationStatusChangeEmail from '@/emails/ApplicationStatusChangeEmail';
import { getSystemSettings } from './settingsActions';
import { createNotification } from './notificationActions';

const APPLICATIONS_COLLECTION = 'studentApplications';

export type CreateApplicationData = Omit<StudentApplication, 'id' | 'applicationDate' | 'status'> & {
  educationDetails?: EducationDetails;
};

// createApplication is public as it's part of the sign-up process
export async function createApplication(applicationData: CreateApplicationData): Promise<StudentApplication> {
  const applicationPayload = {
    ...applicationData,
    applicationDate: FieldValue.serverTimestamp(),
    status: 'Pending',
    educationDetails: applicationData.educationDetails || {},
    specialization: applicationData.specialization || null,
  };

  const docRef = await db.collection(APPLICATIONS_COLLECTION).add(applicationPayload);
  
  const newDoc = await docRef.get();
  const newAppData = newDoc.data();

  if (!newAppData) {
      throw new Error("Could not retrieve created application data.");
  }

  const newApplication: StudentApplication = {
    id: docRef.id,
    ...newAppData,
    applicationDate: newAppData.applicationDate.toDate().toISOString().split('T')[0],
  } as StudentApplication;
  
  // Send notification email to admin
  const course = await getCourseById(newApplication.desiredCourse);
  const systemSettings = await getSystemSettings();
  const adminEmail = process.env.ADMIN_EMAIL;

  if (systemSettings.sendNewApplicationEmail && adminEmail && course) {
    try {
        await sendEmail({
            to: adminEmail,
            subject: `New Application Received: ${newApplication.fullName}`,
            reactElement: React.createElement(ApplicationSubmittedEmail, {
                applicantName: newApplication.fullName,
                courseName: course.name,
                applicationDate: newApplication.applicationDate,
                applicationId: newApplication.id,
            }),
        });

    } catch (emailError) {
        console.error("Failed to send new application notification email:", emailError);
        // We don't throw here, as the primary action (creating the application) succeeded.
    }
  } else {
      // Email notifications are disabled or admin email not configured
  }

  revalidatePath('/applications'); 
  return JSON.parse(JSON.stringify(newApplication));
}

// getApplications is restricted
export async function getApplications(idToken: string): Promise<StudentApplication[]> {
  const { uid, token } = await requireAuth(idToken, ['admin', 'accountant', 'student']);
  
  const userRole = token.role as User['role'];
  
  // Let is required here to allow reassignment.
  // eslint-disable-next-line prefer-const
  let query: FirebaseFirestore.Query = db.collection(APPLICATIONS_COLLECTION);

  if (userRole === 'student') {
    // For students, filter by their UID. We will sort in-memory later to avoid composite index requirement.
    query = query.where('userId', '==', uid);
  } else {
    // For admins/accountants, order all applications by date. This is efficient.
    query = query.orderBy('applicationDate', 'desc');
  }
  
  const snapshot = await query.get();

  if (snapshot.empty) {
    return [];
  }

  const applications: StudentApplication[] = snapshot.docs.map(doc => {
    const data = doc.data();
    return {
      id: doc.id,
      ...data,
      applicationDate: data.applicationDate.toDate().toISOString().split('T')[0],
      // Handle date of birth if it's a timestamp
      dateOfBirth: data.dateOfBirth?.toDate ? data.dateOfBirth.toDate().toISOString().split('T')[0] : data.dateOfBirth,
    } as StudentApplication;
  });

  // If the user is a student, we sort their (small number of) applications in memory.
  if (userRole === 'student') {
      applications.sort((a, b) => new Date(b.applicationDate).getTime() - new Date(a.applicationDate).getTime());
  }

  return JSON.parse(JSON.stringify(applications)); 
}

// updateApplicationStatus should be restricted
export async function updateApplicationStatus(applicationId: string, newStatus: StudentApplication['status'], idToken: string): Promise<StudentApplication | null> {
  await requireAuth(idToken, ['admin']); // Only admins can change application status for now
  
  const appRef = db.collection(APPLICATIONS_COLLECTION).doc(applicationId);
  const appDoc = await appRef.get();

  if (!appDoc.exists) {
      return null;
  }

  const application = appDoc.data() as StudentApplication;
  const oldStatus = application.status;
  
  await appRef.update({ status: newStatus });

  // If status is changed to "Accepted", generate fee records.
  if (newStatus === 'Accepted' && oldStatus !== 'Accepted') {
      if (application.userId && application.desiredCourse) {
        const course = await getCourseById(application.desiredCourse);
        if (course) {
          const systemSettings = await getSystemSettings();
          const feeStructure = await getCourseFeeStructureByCourseId(application.desiredCourse, idToken);

          if (feeStructure && feeStructure.items.length > 0) {
            console.log(`Generating fees for ${application.fullName} for course ${course.name}`);
            for (const item of feeStructure.items) {
              const totalAmount = (item.tuitionFee || 0) + (item.examFee || 0) + (item.otherFee || 0);
              const feeData: CreateStudentFeeData = {
                studentId: application.userId,
                studentName: application.fullName,
                courseId: application.desiredCourse,
                courseName: course.name, 
                feeDescription: item.name,
                amountDue: totalAmount,
                // Use the new dynamic due date setting
                dueDate: format(addDays(new Date(), systemSettings.defaultDueDateDays), 'yyyy-MM-dd'),
                tuitionFee: item.tuitionFee,
                examFee: item.examFee,
                otherFee: item.otherFee,
              };
              try {
                // createStudentFee is now a Firestore action.
                await createStudentFee(feeData, idToken);
                console.log(`Created fee: ${item.name} for ${application.fullName}`);
              } catch (e) {
                console.error(`Error creating fee ${item.name} for ${application.fullName}:`, e);
              }
            }
          } else {
            console.log(`No fee structure found or no items in fee structure for course ${course.name} (ID: ${application.desiredCourse}). No fees generated.`);
          }
        } else {
          console.warn(`Course with ID ${application.desiredCourse} not found for fee generation.`);
        }
      } else {
        console.warn('Cannot generate fees: Application missing userId or desiredCourse.');
      }
    }

  // If status is changed to "Accepted" or "Rejected", send notification email and create in-app notification for student.
  if ((newStatus === 'Accepted' || newStatus === 'Rejected') && newStatus !== oldStatus) {
      const course = await getCourseById(application.desiredCourse);

      // Send email
      if (application.email && course) {
          try {
              await sendEmail({
                  to: application.email,
                  subject: `Your EduLite Application Status Update`,
                  reactElement: React.createElement(ApplicationStatusChangeEmail, {
                      applicantName: application.fullName,
                      courseName: course.name,
                      status: newStatus,
                      applicationDate: application.applicationDate,
                  }),
              });
              console.log(`Application status update email sent to ${application.email}`);
          } catch(emailError) {
              console.error("Failed to send application status update email:", emailError);
          }
      }

      // Create an in-app notification
      if (application.userId && course) {
        try {
            await createNotification({
                userId: application.userId,
                message: `Your application for ${course.name} has been ${newStatus.toLowerCase()}.`,
                link: `/my-courses`
            });
            console.log(`In-app notification created for application status change for user ${application.userId}`);
        } catch(notifError) {
            console.error("Failed to create in-app notification for application status:", notifError);
        }
      }
  }

  const updatedDoc = await appRef.get();
  const updatedApplicationData = updatedDoc.data();
  if (!updatedApplicationData) return null;

  const updatedApplication: StudentApplication = {
      id: updatedDoc.id,
      ...updatedApplicationData,
      applicationDate: updatedApplicationData.applicationDate.toDate().toISOString().split('T')[0],
      dateOfBirth: updatedApplicationData.dateOfBirth?.toDate ? updatedApplicationData.dateOfBirth.toDate().toISOString().split('T')[0] : updatedApplicationData.dateOfBirth,
  } as StudentApplication;

  revalidatePath('/applications');
  if (newStatus === 'Accepted') {
    revalidatePath('/fee-management');
    revalidatePath('/my-courses');
    revalidatePath('/my-fees');
  }
  return JSON.parse(JSON.stringify(updatedApplication));
}

export async function deleteApplication(applicationId: string, idToken: string): Promise<{ success: boolean }> {
  await requireAuth(idToken, ['admin']); // Only admins can delete applications

  const appRef = db.collection(APPLICATIONS_COLLECTION).doc(applicationId);
  const appDoc = await appRef.get();

  if (!appDoc.exists) {
      throw new Error("Application not found.");
  }
  
  await appRef.delete();

  revalidatePath('/applications');
  return { success: true };
}
