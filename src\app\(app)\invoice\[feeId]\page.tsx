
'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { getStudentFeeById } from '@/actions/feeActions';
import { getSystemSettings } from '@/actions/settingsActions';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { AlertTriangle, ArrowLeft, Loader2, GraduationCap } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { format } from 'date-fns';
import { Logo } from '@/components/navigation/Logo';
import { InvoiceActions } from '@/components/invoice/InvoiceActions';
import type { StudentFee, SystemSettings, User } from '@/types';
import { auth } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';

const statusVariantMap: Record<StudentFee['status'], 'default' | 'secondary' | 'outline' | 'destructive'> = {
  Pending: 'default',
  'Partially Paid': 'secondary',
  Paid: 'outline',
  Overdue: 'destructive',
};

export default function InvoicePage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const feeId = params.feeId as string;

  const [fee, setFee] = useState<StudentFee | null>(null);
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchInvoiceData = useCallback(async () => {
    if (!feeId) return;
    setIsLoading(true);
    setError(null);
    try {
      if (!auth.currentUser) {
        throw new Error("Authentication required to view receipts.");
      }
      const idToken = await auth.currentUser.getIdToken();
      
      const [fetchedFee, fetchedSettings] = await Promise.all([
        getStudentFeeById(feeId, idToken),
        getSystemSettings()
      ]);

      if (!fetchedFee) {
        throw new Error("Receipt not found or you don't have permission to view it.");
      }
      setFee(fetchedFee);
      setSettings(fetchedSettings);
    } catch (e: any) {
      console.error("Failed to fetch receipt:", e);
      setError(e.message || "An error occurred while fetching the receipt.");
      toast({
        title: "Error",
        description: e.message || "Could not load the receipt.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [feeId, toast]);

  useEffect(() => {
    fetchInvoiceData();
  }, [fetchInvoiceData]);

  useEffect(() => {
    if (fee) {
      document.title = `Receipt ${fee.id} - ${fee.studentName} | EduLite`;
    } else {
      document.title = "Receipt | EduLite";
    }
  }, [fee]);


  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto p-4 md:p-6 lg:p-8 space-y-8 animate-pulse">
        <div className="flex justify-between items-start mb-8">
            <div className="space-y-2">
                <Skeleton className="h-8 w-32" />
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-4 w-40" />
            </div>
            <Skeleton className="h-12 w-40" />
        </div>
        <Separator />
         <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-6 w-40" />
            </div>
             <div className="space-y-2 text-right">
                <Skeleton className="h-4 w-48 ml-auto" />
                <Skeleton className="h-4 w-48 ml-auto" />
                <Skeleton className="h-4 w-48 ml-auto" />
            </div>
        </div>
        <Card>
            <CardHeader><Skeleton className="h-6 w-1/2" /></CardHeader>
            <CardContent className="space-y-2">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
            </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !fee || !settings) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)] text-center p-4">
        <AlertTriangle className="h-16 w-16 text-destructive mb-4" />
        <h1 className="text-2xl font-bold text-destructive mb-2">Receipt Not Found</h1>
        <p className="text-muted-foreground mb-6">{error || "The fee record you are looking for does not exist or may have been removed."}</p>
        <Button
          variant="outline"
          onClick={() => {
            if (user?.role === 'admin' || user?.role === 'accountant') {
              router.push('/fee-management');
            } else {
              router.push('/my-fees');
            }
          }}
        >
          <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
        </Button>
      </div>
    );
  }

  const invoiceDate = fee.paymentDate ? format(new Date(fee.paymentDate), 'PPP') : format(new Date(), 'PPP');
  const amountRemaining = fee.amountDue - fee.amountPaid;
  const hasBifurcation = (fee.tuitionFee ?? 0) > 0 || (fee.examFee ?? 0) > 0 || (fee.otherFee ?? 0) > 0;

  return (
    <div className="max-w-4xl mx-auto p-4 md:p-6 lg:p-8">
      {/* Back Navigation - Hidden when printing */}
      <div className="no-print mb-6">
        <Button
          variant="outline"
          className="mb-4"
          onClick={() => {
            // Smart back navigation based on user role
            if (user?.role === 'admin' || user?.role === 'accountant') {
              router.push('/fee-management');
            } else {
              router.push('/my-fees');
            }
          }}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          {user?.role === 'admin' || user?.role === 'accountant' ? 'Back to Fee Management' : 'Back to My Fees'}
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow-xl invoice-print-area p-4 md:p-6 lg:p-8">
      {/* Receipt Header with Institution on left, RECEIPT in center, EduLite branding on right */}
      <div className="grid grid-cols-3 items-start mb-8">
        {/* Left: Institution Details */}
        <div>
           {settings.logoUrl ? (
            <>
              <div className="relative h-12 w-40 mb-2">
                <Image
                  src={settings.logoUrl}
                  alt={settings.institutionName}
                  fill
                  className="object-contain object-left"
                  sizes="(max-width: 768px) 100vw, 160px"
                />
              </div>
              <h2 className="text-lg font-headline font-semibold text-primary mb-1">{settings.institutionName}</h2>
            </>
          ) : (
            <div className="flex items-center gap-2 mb-2">
              <GraduationCap className="h-8 w-8 text-accent" />
              <span className="text-2xl font-headline font-semibold text-primary">{settings.institutionName}</span>
            </div>
          )}
          <p className="text-sm text-muted-foreground mt-1 whitespace-pre-line">
            {settings.institutionAddress}<br />
            {settings.institutionEmail}
          </p>
        </div>

        {/* Center: RECEIPT Title */}
        <div className="flex justify-center">
          <h1 className="text-4xl font-headline font-bold text-primary tracking-tight">RECEIPT</h1>
        </div>

        {/* Right: EduLite Branding */}
        <div className="flex justify-end">
          <div className="text-right">
            <div className="flex items-center justify-end gap-3">
              <GraduationCap className="h-8 w-8 text-accent" />
              <span className="text-3xl font-headline font-bold text-primary">EduLite</span>
            </div>
          </div>
        </div>
      </div>

      <Separator className="my-6" />

      <div className="grid md:grid-cols-2 gap-8 mb-8">
        <div>
          <h2 className="text-sm font-semibold text-muted-foreground uppercase tracking-wider mb-1">Bill To</h2>
          <p className="text-lg font-medium text-foreground">{fee.studentName}</p>
          <p className="text-sm text-muted-foreground">Student ID: {fee.studentId}</p>
        </div>
        <div className="text-left md:text-right">
          <p><span className="font-semibold text-muted-foreground">Receipt #:</span> {fee.id}</p>
          <p><span className="font-semibold text-muted-foreground">Receipt Date:</span> {invoiceDate}</p>
          <p><span className="font-semibold text-muted-foreground">Due Date:</span> {format(new Date(fee.dueDate), 'PPP')}</p>
        </div>
      </div>

      <Card className="mb-8 border-border">
        <CardHeader>
          <CardTitle className="font-headline text-lg">Fee Details for {fee.courseName} - {fee.feeDescription}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-border">
                  <th className="py-2 px-3 text-left font-semibold text-muted-foreground">Description</th>
                  <th className="py-2 px-3 text-right font-semibold text-muted-foreground">Amount (₹)</th>
                </tr>
              </thead>
              <tbody>
                {hasBifurcation ? (
                  <>
                    {(fee.tuitionFee ?? 0) > 0 && (
                      <tr className="border-b border-border/50">
                        <td className="py-3 px-3">Tuition Fee</td>
                        <td className="py-3 px-3 text-right">₹{fee.tuitionFee!.toFixed(2)}</td>
                      </tr>
                    )}
                    {(fee.examFee ?? 0) > 0 && (
                      <tr className="border-b border-border/50">
                        <td className="py-3 px-3">Exam Fee</td>
                        <td className="py-3 px-3 text-right">₹{fee.examFee!.toFixed(2)}</td>
                      </tr>
                    )}
                    {(fee.otherFee ?? 0) > 0 && (
                      <tr className="border-b border-border/50">
                        <td className="py-3 px-3">Other Fee</td>
                        <td className="py-3 px-3 text-right">₹{fee.otherFee!.toFixed(2)}</td>
                      </tr>
                    )}
                  </>
                ) : (
                  <tr className="border-b border-border/50">
                    <td className="py-3 px-3">{fee.feeDescription}</td>
                    <td className="py-3 px-3 text-right">₹{fee.amountDue.toFixed(2)}</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <div className="grid md:grid-cols-2 gap-8 items-start">
        <div>
          <h3 className="text-md font-semibold text-muted-foreground mb-2">Payment Status</h3>
          <Badge variant={statusVariantMap[fee.status]} className="text-lg px-3 py-1 capitalize">
            {fee.status}
          </Badge>
          {fee.lastPaymentDate && (
            <p className="text-xs text-muted-foreground mt-1">
              Last Payment: {format(new Date(fee.lastPaymentDate), 'PPP')}
            </p>
          )}
        </div>
        <div className="space-y-2 text-right">
          <div className="flex justify-between items-center">
            <p className="text-md text-muted-foreground">Subtotal:</p>
            <p className="text-md font-semibold text-foreground">₹{fee.amountDue.toFixed(2)}</p>
          </div>
          <div className="flex justify-between items-center">
            <p className="text-md text-muted-foreground">Amount Paid:</p>
            <p className="text-md font-semibold text-foreground">₹{fee.amountPaid.toFixed(2)}</p>
          </div>
          <Separator />
          <div className="flex justify-between items-center text-lg">
            <p className="font-bold text-primary">Amount Remaining:</p>
            <p className="font-bold text-primary">₹{amountRemaining.toFixed(2)}</p>
          </div>
        </div>
      </div>

      <Separator className="my-8" />

      <div className="text-center text-sm text-muted-foreground mt-6">
        <p>Thank you for your prompt payment. For any inquiries, please contact {settings.institutionEmail}.</p>
      </div>

      <InvoiceActions />
      </div>
    </div>
  );
}
