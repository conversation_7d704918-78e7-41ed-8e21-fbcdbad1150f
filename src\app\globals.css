@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 210 17% 94%; /* Light Gray #ECEFF1 */
    --foreground: 220 10% 20%; /* Darker gray for text */

    --card: 0 0% 100%; /* White */
    --card-foreground: 220 10% 20%;

    --popover: 0 0% 100%; /* White */
    --popover-foreground: 220 10% 20%;

    --primary: 231 48% 48%; /* Deep Blue #3F51B5 */
    --primary-foreground: 0 0% 100%; /* White */

    --secondary: 220 15% 85%;
    --secondary-foreground: 220 10% 20%;

    --muted: 220 15% 88%;
    --muted-foreground: 220 10% 45%;

    --accent: 174 100% 29%; /* Teal #009688 */
    --accent-foreground: 0 0% 100%; /* White */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 15% 80%;
    --input: 220 15% 80%;
    --ring: 231 48% 48%; /* Primary color for focus rings */

    --radius: 0.5rem;

    /* Sidebar specific variables, aligned with the theme */
    --sidebar-background: 0 0% 100%; /* White sidebar background */
    --sidebar-foreground: 220 10% 20%; /* Dark text */
    --sidebar-primary: 231 48% 48%; /* Deep Blue for primary elements (e.g., active link bg or icon color) */
    --sidebar-primary-foreground: 0 0% 100%; /* White text on deep blue */
    --sidebar-accent: 174 100% 29%; /* Teal for accent (e.g., hover on links) */
    --sidebar-accent-foreground: 0 0% 100%; /* White text on teal */
    --sidebar-border: 220 15% 88%; /* Light border for sidebar */
    --sidebar-ring: 174 100% 29%; /* Teal for focus rings within sidebar */

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    /* Keeping dark mode defaults for now, can be updated later if needed */
    --background: 220 10% 10%;
    --foreground: 0 0% 98%;

    --card: 220 10% 10%;
    --card-foreground: 0 0% 98%;

    --popover: 220 10% 10%;
    --popover-foreground: 0 0% 98%;

    --primary: 231 48% 58%; /* Lighter Deep Blue for dark mode */
    --primary-foreground: 0 0% 10%;

    --secondary: 220 10% 20%;
    --secondary-foreground: 0 0% 98%;

    --muted: 220 10% 20%;
    --muted-foreground: 0 0% 60%;

    --accent: 174 100% 39%; /* Lighter Teal for dark mode */
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 10% 20%;
    --input: 220 10% 20%;
    --ring: 231 48% 58%;

    /* Dark mode sidebar variables */
    --sidebar-background: 220 10% 15%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 231 48% 58%;
    --sidebar-primary-foreground: 0 0% 10%;
    --sidebar-accent: 174 100% 39%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 220 10% 25%;
    --sidebar-ring: 174 100% 39%;

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@media print {
  body * {
    visibility: hidden;
  }
  body {
    background: white !important;
    background-color: white !important;
  }
  .invoice-print-area, .invoice-print-area * {
    visibility: visible;
  }
  .invoice-print-area {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    margin: 0;
    padding: 0;
    border: none;
    box-shadow: none;
    background: white !important;
    background-color: white !important;
    border-radius: 0 !important;
  }
  .no-print {
    display: none !important;
  }
  /* Ensure all elements have white background when printing */
  * {
    background: white !important;
    background-color: white !important;
    color-adjust: exact !important;
    -webkit-print-color-adjust: exact !important;
  }

  /* Force grid layout and text alignment in print mode */
  .invoice-print-area .grid {
    display: grid !important;
  }
  .invoice-print-area .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }
  .invoice-print-area .md\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
  .invoice-print-area .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }
  .invoice-print-area .text-right {
    text-align: right !important;
  }
  .invoice-print-area .justify-center {
    justify-content: center !important;
  }
  .invoice-print-area .justify-end {
    justify-content: flex-end !important;
  }
  .invoice-print-area .flex {
    display: flex !important;
  }
}
