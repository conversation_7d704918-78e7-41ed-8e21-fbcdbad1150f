
'use server';

import { revalidatePath } from 'next/cache';
import { db, adminAuth } from '@/lib/firebase-admin';
import { FieldValue } from 'firebase-admin/firestore';
import type { StudentFee, FeeStatus, CourseFeeStructure, FeeItem, Course, StudentApplication, User, PaymentTransaction } from '@/types';
import { format, addDays } from 'date-fns';
import { requireAuth } from '@/lib/authUtils';
import { sendEmail } from '@/lib/email';
import React from 'react';
import FeeReminderEmail from '@/emails/FeeReminderEmail';
import OverdueFeeReminderEmail from '@/emails/OverdueFeeReminderEmail';
import { createNotification } from './notificationActions';
import { getSystemSettings } from './settingsActions';
import PaymentConfirmationEmail from '@/emails/PaymentConfirmationEmail';

const FEES_COLLECTION = 'studentFees';
const COURSE_FEE_STRUCTURES_COLLECTION = 'courseFeeStructures';
const PAYMENT_TRANSACTIONS_COLLECTION = 'paymentTransactions';

const mapFeeFromDoc = (doc: FirebaseFirestore.DocumentSnapshot): StudentFee => {
    const data = doc.data() as Omit<StudentFee, 'id'>;
    return {
        ...data,
        id: doc.id,
        dueDate: data.dueDate, // Already a string
        paymentDate: data.paymentDate,
        lastPaymentDate: data.lastPaymentDate,
    };
};

const mapPaymentTransactionFromDoc = (doc: FirebaseFirestore.DocumentSnapshot): PaymentTransaction => {
    const data = doc.data() as Omit<PaymentTransaction, 'id'>;
    return {
        ...data,
        id: doc.id,
    };
};

export async function getStudentFees(idToken: string): Promise<StudentFee[]> {
  const { uid, token } = await requireAuth(idToken, ['admin', 'accountant', 'student']);
  
  const userRole = token.role as User['role'];
  
  let query: FirebaseFirestore.Query = db.collection(FEES_COLLECTION);
  
  if (userRole === 'student') {
    query = query.where('studentId', '==', uid);
  }
  
  const snapshot = await query.get();

  if (snapshot.empty) {
    return [];
  }
  
  const fees: StudentFee[] = snapshot.docs.map(mapFeeFromDoc);
  
  // Sort in memory after fetching
  fees.sort((a, b) => {
      const dateDiff = new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime();
      if (dateDiff !== 0) return dateDiff;
      return a.studentName.localeCompare(b.studentName);
  });
  
  return JSON.parse(JSON.stringify(fees));
}

export async function getStudentFeeById(feeId: string, idToken?: string): Promise<StudentFee | null> {
  // Allow fetching without token for webhook, but secure it if token is present
  let uid: string | null = null;
  if (idToken) {
    const authResult = await requireAuth(idToken, ['admin', 'accountant', 'student']);
    uid = authResult.uid;
  }
  
  const feeDoc = await db.collection(FEES_COLLECTION).doc(feeId).get();
  if (!feeDoc.exists) {
    return null;
  }
  
  const fee = mapFeeFromDoc(feeDoc);
  
  // Security check: If the user is a student, ensure they are the owner of the fee record.
  if (uid && fee.studentId !== uid) {
      const { token } = await requireAuth(idToken!, ['admin', 'accountant']); // Re-auth to check role
      if (token.role !== 'admin' && token.role !== 'accountant') {
          throw new Error("Forbidden: You do not have permission to view this fee record.");
      }
  }

  return JSON.parse(JSON.stringify(fee));
}

export async function getStudentFeesByStudentId(studentId: string, idToken: string): Promise<StudentFee[]> {
  const { uid, token } = await requireAuth(idToken, ['admin', 'accountant', 'student']);
  // A student can only view their own fees. An admin/accountant can view any student's fees.
  if (token.role === 'student' && uid !== studentId) {
     throw new Error("Forbidden: You can only view your own fee details.");
  }

  const feesSnapshot = await db.collection(FEES_COLLECTION).where('studentId', '==', studentId).get();
  if (feesSnapshot.empty) {
    return [];
  }
  const fees = feesSnapshot.docs.map(mapFeeFromDoc);
  fees.sort((a, b) => new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime());
  return JSON.parse(JSON.stringify(fees));
}

export type CreateStudentFeeData = Omit<StudentFee, 'id' | 'amountPaid' | 'status' | 'paymentDate' | 'lastPaymentDate'>;

export async function createStudentFee(data: CreateStudentFeeData, idToken: string): Promise<StudentFee> {
  await requireAuth(idToken, ['admin']);

  const newFeeData = {
    ...data,
    amountPaid: 0,
    status: 'Pending' as FeeStatus,
    createdAt: FieldValue.serverTimestamp(),
    tuitionFee: data.tuitionFee || 0,
    examFee: data.examFee || 0,
    otherFee: data.otherFee || 0,
  };
  
  const docRef = await db.collection(FEES_COLLECTION).add(newFeeData);

  try {
    await createNotification({
        userId: data.studentId,
        message: `A new fee of ₹${data.amountDue.toFixed(2)} for "${data.feeDescription}" has been added.`,
        link: `/my-fees`
    });
  } catch(notifError) {
    console.error("Failed to create in-app notification for new fee:", notifError);
  }

  const newFee: StudentFee = {
    id: docRef.id,
    ...data,
    amountPaid: 0,
    status: 'Pending',
    tuitionFee: data.tuitionFee || 0,
    examFee: data.examFee || 0,
    otherFee: data.otherFee || 0,
  }

  revalidatePath('/fee-management');
  revalidatePath(`/my-courses/${data.courseId}`);
  revalidatePath('/my-fees');
  return JSON.parse(JSON.stringify(newFee));
}

export async function recordManualPayment(
  { feeId, amountPaid, paymentDate, notes, paymentId, paymentMethod = 'Cash' }:
  { feeId: string; amountPaid: number; paymentDate: string; notes?: string, paymentId?: string, paymentMethod?: PaymentTransaction['paymentMethod'] },
  idToken?: string | null // idToken is optional, not needed for webhook calls
): Promise<StudentFee> {
    // If idToken is provided, it's a manual payment from an admin/accountant.
    // If not, it's from a webhook, which is verified by its signature.
    if (idToken) {
        await requireAuth(idToken, ['admin', 'accountant']);
    }

    const feeRef = db.collection(FEES_COLLECTION).doc(feeId);

    // Get current user for recordedBy field
    let recordedBy = 'System';
    if (idToken) {
        try {
            const decodedToken = await adminAuth.verifyIdToken(idToken);
            recordedBy = decodedToken.email || decodedToken.uid;
        } catch (error) {
            console.error('Error getting user info:', error);
        }
    }

    // Use a transaction to ensure atomic read/write
    const updatedFee = await db.runTransaction(async (transaction) => {
        const feeDoc = await transaction.get(feeRef);
        if (!feeDoc.exists) {
            throw new Error(`Fee record with ID ${feeId} not found.`);
        }

        const fee = feeDoc.data() as StudentFee;
        const newAmountPaid = (fee.amountPaid || 0) + amountPaid;
        let newStatus: FeeStatus = 'Partially Paid';

        if (newAmountPaid >= fee.amountDue) {
            newStatus = 'Paid';
        }

        const updates: any = {
          amountPaid: newAmountPaid,
          status: newStatus,
          lastPaymentDate: paymentDate,
        };
        if (newStatus === 'Paid') {
            updates.paymentDate = paymentDate;
        }

        transaction.update(feeRef, updates);

        // Create payment transaction record
        const paymentTransactionData: Omit<PaymentTransaction, 'id'> = {
            feeId: feeId,
            studentId: fee.studentId,
            studentName: fee.studentName,
            amount: amountPaid,
            paymentDate: paymentDate,
            paymentMethod: paymentMethod,
            transactionId: paymentId || null,
            notes: notes || null,
            recordedBy: recordedBy,
            recordedAt: new Date().toISOString(),
        };

        const paymentTransactionRef = db.collection(PAYMENT_TRANSACTIONS_COLLECTION).doc();
        transaction.set(paymentTransactionRef, paymentTransactionData);

        return { ...fee, ...updates } as StudentFee;
    });

    const paymentType = idToken ? 'manual' : 'online';
    const notificationMessage = `${paymentType === 'manual' ? 'A manual' : 'An online'} payment of ₹${amountPaid.toFixed(2)} was recorded for "${updatedFee.feeDescription}".`;
    
    try {
        await createNotification({
            userId: updatedFee.studentId,
            message: notificationMessage,
            link: `/invoice/${updatedFee.id}`
        });
    } catch (notifError) {
        console.error("Failed to create payment notification:", notifError);
    }
    
    // Send email confirmation for online payments
    if(paymentType === 'online' && paymentId) {
        try {
            const userRecord = await adminAuth.getUser(updatedFee.studentId);
            if (userRecord.email) {
                await sendEmail({
                    to: userRecord.email,
                    subject: `Payment Confirmation for ${updatedFee.courseName}`,
                    reactElement: React.createElement(PaymentConfirmationEmail, {
                        studentName: updatedFee.studentName,
                        courseName: updatedFee.courseName,
                        feeDescription: updatedFee.feeDescription,
                        amountPaid: amountPaid,
                        paymentId: paymentId,
                        paymentDate: paymentDate,
                    })
                });
            }
        } catch (emailError) {
            console.error("Failed to send payment confirmation email:", emailError);
        }
    }


    revalidatePath('/fee-management');
    revalidatePath(`/invoice/${feeId}`);
    revalidatePath(`/my-courses/${updatedFee.courseId}`);
    revalidatePath('/financial-reports');
    revalidatePath('/my-fees');
    
    // We need to remap the doc to get a clean object for JSON serialization
    const finalDoc = await feeRef.get();
    return JSON.parse(JSON.stringify(mapFeeFromDoc(finalDoc)));
}

export async function updateFeeStatus(feeId: string, newStatus: FeeStatus, idToken: string): Promise<StudentFee | null> {
  await requireAuth(idToken, ['admin', 'accountant']);
  
  const feeRef = db.collection(FEES_COLLECTION).doc(feeId);
  const feeDoc = await feeRef.get();

  if (!feeDoc.exists) return null;
  const fee = mapFeeFromDoc(feeDoc);

  const updates: any = { status: newStatus };

  if (newStatus === 'Paid') {
    updates.paymentDate = fee.paymentDate || format(new Date(), 'yyyy-MM-dd');
    updates.amountPaid = fee.amountDue; // Mark as fully paid
  }

  // Clear payment date if moving away from Paid status
  if (newStatus !== 'Paid' && fee.paymentDate) {
    updates.paymentDate = FieldValue.delete();
  }

  await feeRef.update(updates);
  
  const updatedDoc = await feeRef.get();
  const updatedFee = mapFeeFromDoc(updatedDoc);

  revalidatePath('/fee-management');
  revalidatePath(`/invoice/${feeId}`);
  revalidatePath(`/my-courses/${updatedFee.courseId}`);
  revalidatePath('/financial-reports');
  revalidatePath('/my-fees');
  
  return JSON.parse(JSON.stringify(updatedFee));
}

export async function deleteStudentFee(feeId: string, idToken: string): Promise<{ success: boolean }> {
  await requireAuth(idToken, ['admin', 'accountant']);
  
  const feeRef = db.collection(FEES_COLLECTION).doc(feeId);
  const feeDoc = await feeRef.get();

  if (!feeDoc.exists) {
    throw new Error("Fee record not found.");
  }

  const feeData = feeDoc.data() as StudentFee;

  await feeRef.delete();

  revalidatePath('/fee-management');
  revalidatePath('/financial-reports');
  revalidatePath('/my-fees');
  if (feeData.courseId) {
    revalidatePath(`/my-courses/${feeData.courseId}`);
  }
  
  return { success: true };
}

export async function sendOverdueFeeReminders(idToken: string): Promise<{ count: number; details?: string }> {
    await requireAuth(idToken, ['admin', 'accountant']);

    const todayString = format(new Date(), 'yyyy-MM-dd');
    console.log(`Checking for overdue fees before date: ${todayString}`);

    // Split the query to avoid composite index requirement
    // First get all pending and partially paid fees
    const pendingQuery = db.collection(FEES_COLLECTION)
        .where('status', '==', 'Pending');
    const partiallyPaidQuery = db.collection(FEES_COLLECTION)
        .where('status', '==', 'Partially Paid');

    const [pendingSnapshot, partiallyPaidSnapshot] = await Promise.all([
        pendingQuery.get(),
        partiallyPaidQuery.get()
    ]);

    // Combine results and filter by due date in memory
    const allFees = [
        ...pendingSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as StudentFee)),
        ...partiallyPaidSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as StudentFee))
    ];

    console.log(`Found ${allFees.length} fees with Pending or Partially Paid status`);

    const overdueFees = allFees.filter(fee => {
        const isOverdue = fee.dueDate < todayString;
        if (isOverdue) {
            console.log(`Overdue fee found: ${fee.id}, due: ${fee.dueDate}, student: ${fee.studentName}`);
        }
        return isOverdue;
    });

    console.log(`Found ${overdueFees.length} overdue fees`);

    if (overdueFees.length === 0) {
        console.log('No overdue fees found');
        return { count: 0, details: 'No overdue fees found in the system' };
    }

    let remindersSent = 0;
    let emailErrors = 0;
    const batch = db.batch();
    const notificationPromises = [];

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    for (const feeData of overdueFees) {
        const fee = feeData as StudentFee;
        console.log(`Processing reminder for fee ${fee.id} - Student: ${fee.studentName}`);
        
        try {
            const userRecord = await adminAuth.getUser(fee.studentId);
            const studentEmail = userRecord.email;

            if (studentEmail) {
                console.log(`Sending reminder email to: ${studentEmail}`);
                const invoiceUrl = `${baseUrl}/invoice/${fee.id}`;

                const emailPromise = sendEmail({
                    to: studentEmail,
                    subject: `URGENT: Fee Payment Overdue for ${fee.courseName}`,
                    reactElement: React.createElement(OverdueFeeReminderEmail, {
                        studentName: fee.studentName,
                        courseName: fee.courseName,
                        feeDescription: fee.feeDescription,
                        amountDue: fee.amountDue - fee.amountPaid,
                        dueDate: fee.dueDate,
                        invoiceUrl: invoiceUrl,
                    }),
                }).then(result => {
                    if (result.success) {
                        console.log(`Email sent successfully to ${studentEmail}`);
                    } else {
                        console.error(`Failed to send email to ${studentEmail}:`, result.error);
                        emailErrors++;
                    }
                    return result;
                }).catch(error => {
                    console.error(`Exception sending email to ${studentEmail}:`, error);
                    emailErrors++;
                    return { success: false, error: error.message };
                });

                notificationPromises.push(emailPromise);

                const notificationPromise = createNotification({
                    userId: fee.studentId,
                    message: `Payment for "${fee.feeDescription}" is overdue. Amount: ₹${(fee.amountDue - fee.amountPaid).toFixed(2)}`,
                    link: `/invoice/${fee.id}`
                }).catch(err => {
                    console.error(`Failed to create notification for fee ${fee.id}:`, err);
                });
                notificationPromises.push(notificationPromise);

                const feeRef = db.collection(FEES_COLLECTION).doc(fee.id);
                batch.update(feeRef, { status: 'Overdue' });

                remindersSent++;
            } else {
                console.warn(`No email found for student ${fee.studentId}`);
            }
        } catch (error) {
            console.error(`Failed to process reminder for fee ${fee.id} and student ${fee.studentId}:`, error);
        }
    }

    console.log(`Processing ${notificationPromises.length} email notifications...`);
    await Promise.all(notificationPromises);
    await batch.commit();

    if (remindersSent > 0) {
        revalidatePath('/fee-management');
        revalidatePath('/financial-reports');
    }

    const details = emailErrors > 0
        ? `${remindersSent} reminders processed, ${emailErrors} email errors occurred`
        : `${remindersSent} reminders sent successfully`;

    console.log(`Reminder process completed: ${details}`);
    return { count: remindersSent, details };
}

/**
 * Send fee reminders to all students with pending or partially paid fees (not just overdue)
 * This allows admins to manually send reminders at any time
 */
export async function sendFeeReminders(idToken: string): Promise<{ count: number; details?: string }> {
    await requireAuth(idToken, ['admin', 'accountant']);

    console.log('Sending fee reminders to all students with pending/partially paid fees...');

    // Get all pending and partially paid fees (regardless of due date)
    const pendingQuery = db.collection(FEES_COLLECTION)
        .where('status', '==', 'Pending');
    const partiallyPaidQuery = db.collection(FEES_COLLECTION)
        .where('status', '==', 'Partially Paid');

    const [pendingSnapshot, partiallyPaidSnapshot] = await Promise.all([
        pendingQuery.get(),
        partiallyPaidQuery.get()
    ]);

    // Combine results
    const allFees = [
        ...pendingSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })),
        ...partiallyPaidSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
    ];

    console.log(`Found ${allFees.length} fees with Pending or Partially Paid status`);

    if (allFees.length === 0) {
        console.log('No pending or partially paid fees found');
        return { count: 0, details: 'No pending or partially paid fees found in the system' };
    }

    let remindersSent = 0;
    let emailErrors = 0;
    const notificationPromises = [];

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    for (const feeData of allFees) {
        const fee = feeData as StudentFee;
        console.log(`Processing reminder for fee ${fee.id} - Student: ${fee.studentName}`);

        try {
            const userRecord = await adminAuth.getUser(fee.studentId);
            const studentEmail = userRecord.email;

            if (studentEmail) {
                console.log(`Sending fee reminder email to: ${studentEmail}`);
                const invoiceUrl = `${baseUrl}/invoice/${fee.id}`;

                const emailPromise = sendEmail({
                    to: studentEmail,
                    subject: `Fee Payment Reminder for ${fee.courseName}`,
                    reactElement: React.createElement(FeeReminderEmail, {
                        studentName: fee.studentName,
                        courseName: fee.courseName,
                        feeDescription: fee.feeDescription,
                        amountDue: fee.amountDue - fee.amountPaid,
                        dueDate: fee.dueDate,
                        invoiceUrl: invoiceUrl,
                    }),
                }).then(result => {
                    if (result.success) {
                        console.log(`Fee reminder email sent successfully to ${studentEmail}`);
                    } else {
                        console.error(`Failed to send fee reminder email to ${studentEmail}:`, result.error);
                        emailErrors++;
                    }
                    return result;
                }).catch(error => {
                    console.error(`Exception sending fee reminder email to ${studentEmail}:`, error);
                    emailErrors++;
                    return { success: false, error: error.message };
                });

                notificationPromises.push(emailPromise);

                const notificationPromise = createNotification({
                    userId: fee.studentId,
                    message: `Reminder: Payment for "${fee.feeDescription}" is due. Amount: ₹${(fee.amountDue - fee.amountPaid).toFixed(2)}`,
                    link: `/invoice/${fee.id}`
                }).catch(err => {
                    console.error(`Failed to create notification for fee ${fee.id}:`, err);
                });
                notificationPromises.push(notificationPromise);

                remindersSent++;
            } else {
                console.warn(`No email found for student ${fee.studentId}`);
            }
        } catch (error) {
            console.error(`Failed to process fee reminder for fee ${fee.id} and student ${fee.studentId}:`, error);
        }
    }

    console.log(`Processing ${notificationPromises.length} fee reminder notifications...`);
    await Promise.all(notificationPromises);

    if (remindersSent > 0) {
        revalidatePath('/fee-management');
        revalidatePath('/financial-reports');
    }

    const details = emailErrors > 0
        ? `${remindersSent} fee reminders processed, ${emailErrors} email errors occurred`
        : `${remindersSent} fee reminders sent successfully`;

    console.log(`Fee reminder process completed: ${details}`);
    return { count: remindersSent, details };
}

/**
 * Update the due date of a specific fee
 */
export async function updateFeeDueDate(feeId: string, newDueDate: string, idToken: string): Promise<StudentFee | null> {
    await requireAuth(idToken, ['admin', 'accountant']);

    const feeRef = db.collection(FEES_COLLECTION).doc(feeId);
    const feeDoc = await feeRef.get();

    if (!feeDoc.exists) return null;

    await feeRef.update({ dueDate: newDueDate });

    const updatedDoc = await feeRef.get();
    const updatedFee = mapFeeFromDoc(updatedDoc);

    // Create notification for student
    try {
        await createNotification({
            userId: updatedFee.studentId,
            message: `Due date for "${updatedFee.feeDescription}" has been updated to ${format(new Date(newDueDate), 'PP')}.`,
            link: `/invoice/${feeId}`
        });
    } catch (notifError) {
        console.error("Failed to create notification for due date update:", notifError);
    }

    revalidatePath('/fee-management');
    revalidatePath('/my-fees');
    return JSON.parse(JSON.stringify(updatedFee));
}

export async function generateFeeForEnrolledStudents(
  courseId: string, 
  feeItem: FeeItem, 
  idToken: string
): Promise<{ success: boolean; count: number }> {
  await requireAuth(idToken, ['admin']);

  const courseDoc = await db.collection('courses').doc(courseId).get();
  if (!courseDoc.exists) {
    throw new Error('Course not found.');
  }
  const courseData = courseDoc.data() as Course;

  const applicationsSnapshot = await db.collection('studentApplications')
    .where('status', '==', 'Accepted')
    .get();

  const enrolledStudentsInCourse = applicationsSnapshot.docs.filter(
    doc => doc.data().desiredCourse === courseId
  );

  if (enrolledStudentsInCourse.length === 0) {
    return { success: true, count: 0 };
  }
  
  let generatedCount = 0;
  const batch = db.batch();
  const notificationPromises = [];
  const totalAmount = (feeItem.tuitionFee || 0) + (feeItem.examFee || 0) + (feeItem.otherFee || 0);
  const systemSettings = await getSystemSettings();

  for (const doc of enrolledStudentsInCourse) {
    const application = doc.data() as StudentApplication;
    if (!application.userId) continue;

    const existingFeeQuery = await db.collection(FEES_COLLECTION)
      .where('studentId', '==', application.userId)
      .where('courseId', '==', courseId)
      .where('feeDescription', '==', feeItem.name)
      .limit(1)
      .get();
      
    if (!existingFeeQuery.empty) {
      console.log(`Skipping fee "${feeItem.name}" for student ${application.fullName}, as it already exists.`);
      continue;
    }

    const feeData: CreateStudentFeeData = {
      studentId: application.userId,
      studentName: application.fullName,
      courseId: courseId,
      courseName: courseData.name,
      feeDescription: feeItem.name,
      amountDue: totalAmount,
      dueDate: format(addDays(new Date(), systemSettings.defaultDueDateDays), 'yyyy-MM-dd'),
      tuitionFee: feeItem.tuitionFee,
      examFee: feeItem.examFee,
      otherFee: feeItem.otherFee,
    };
    
    const newFeePayload = {
        ...feeData,
        amountPaid: 0,
        status: 'Pending' as FeeStatus,
        createdAt: FieldValue.serverTimestamp()
    };
    const newFeeRef = db.collection(FEES_COLLECTION).doc();
    batch.set(newFeeRef, newFeePayload);

    const notificationPromise = createNotification({
        userId: feeData.studentId,
        message: `A new fee of ₹${feeData.amountDue.toFixed(2)} for "${feeData.feeDescription}" has been added.`,
        link: `/invoice/${newFeeRef.id}`
    }).catch(err => console.error(`Failed to create notification for student ${feeData.studentId}:`, err));
    notificationPromises.push(notificationPromise);
    
    generatedCount++;
  }

  if (generatedCount > 0) {
    await batch.commit();
    await Promise.all(notificationPromises);
    
    revalidatePath('/fee-management');
    revalidatePath('/my-fees');
    revalidatePath('/financial-reports');
  }

  return { success: true, count: generatedCount };
}

export interface FinancialReportFilters {
  startDate?: string;
  endDate?: string;
  courseId?: string;
  status?: FeeStatus;
}

export async function getFinancialReportData(filters: FinancialReportFilters, idToken: string): Promise<StudentFee[]> {
  await requireAuth(idToken, ['admin', 'accountant']);

  // Get all data without any complex queries to avoid index requirements
  const snapshot = await db.collection(FEES_COLLECTION).get();

  if (snapshot.empty) {
    return [];
  }

  let fees = snapshot.docs.map(mapFeeFromDoc);

  // Apply client-side filtering
  if (filters.status) {
    fees = fees.filter(fee => fee.status === filters.status);
  }

  if (filters.courseId) {
    fees = fees.filter(fee => fee.courseId === filters.courseId);
  }

  if (filters.startDate) {
    fees = fees.filter(fee => fee.dueDate >= filters.startDate!);
  }

  if (filters.endDate) {
    fees = fees.filter(fee => fee.dueDate <= filters.endDate!);
  }

  // Sort client-side
  fees.sort((a, b) => new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime());

  return JSON.parse(JSON.stringify(fees));
}

// --- Payment Transaction Actions ---

export async function getPaymentTransactionsByFeeId(feeId: string, idToken: string): Promise<PaymentTransaction[]> {
  await requireAuth(idToken, ['admin', 'accountant']);

  const snapshot = await db.collection(PAYMENT_TRANSACTIONS_COLLECTION)
    .where('feeId', '==', feeId)
    .orderBy('paymentDate', 'desc')
    .get();

  if (snapshot.empty) {
    return [];
  }

  const transactions = snapshot.docs.map(mapPaymentTransactionFromDoc);
  return JSON.parse(JSON.stringify(transactions));
}

export async function getAllPaymentTransactions(idToken: string): Promise<PaymentTransaction[]> {
  await requireAuth(idToken, ['admin', 'accountant']);

  const snapshot = await db.collection(PAYMENT_TRANSACTIONS_COLLECTION)
    .orderBy('paymentDate', 'desc')
    .get();

  if (snapshot.empty) {
    return [];
  }

  const transactions = snapshot.docs.map(mapPaymentTransactionFromDoc);
  return JSON.parse(JSON.stringify(transactions));
}

export async function getCoursesWithFees(idToken: string): Promise<Course[]> {
  await requireAuth(idToken, ['admin', 'accountant']);

  // Get all fee records to find which courses have fees
  const feesSnapshot = await db.collection(FEES_COLLECTION).get();

  if (feesSnapshot.empty) {
    return [];
  }

  // Extract unique course IDs from fee records
  const courseIds = new Set<string>();
  feesSnapshot.docs.forEach(doc => {
    const fee = doc.data() as StudentFee;
    if (fee.courseId) {
      courseIds.add(fee.courseId);
    }
  });

  if (courseIds.size === 0) {
    return [];
  }

  // Get course details for these course IDs
  const courses: Course[] = [];
  for (const courseId of courseIds) {
    const courseDoc = await db.collection('courses').doc(courseId).get();
    if (courseDoc.exists) {
      const courseData = courseDoc.data() as Omit<Course, 'id'>;
      courses.push({
        ...courseData,
        id: courseDoc.id,
      });
    }
  }

  return JSON.parse(JSON.stringify(courses));
}

// --- Course Fee Structure Actions ---

export async function getCourseFeeStructureByCourseId(courseId: string, idToken: string): Promise<CourseFeeStructure | null> {
  await requireAuth(idToken, ['admin']);
  
  const docRef = db.collection(COURSE_FEE_STRUCTURES_COLLECTION).doc(courseId);
  const docSnap = await docRef.get();

  if (docSnap.exists) {
    return JSON.parse(JSON.stringify({ id: docSnap.id, ...docSnap.data() } as CourseFeeStructure));
  } else {
    const newStructure: CourseFeeStructure = {
      id: courseId,
      courseId: courseId,
      items: [],
    };
    return JSON.parse(JSON.stringify(newStructure));
  }
}

export async function saveCourseFeeStructure(courseId: string, items: FeeItem[], idToken: string): Promise<CourseFeeStructure | null> {
  await requireAuth(idToken, ['admin']);

  const sanitizedItems = items.map(item => ({
    id: item.id || `item-${Date.now()}-${Math.random().toString(36).substring(2,7)}`,
    name: item.name,
    tuitionFee: item.tuitionFee || 0,
    examFee: item.examFee || 0,
    otherFee: item.otherFee || 0,
  }));
  
  const structure: CourseFeeStructure = {
      id: courseId,
      courseId: courseId,
      items: sanitizedItems
  };

  const docRef = db.collection(COURSE_FEE_STRUCTURES_COLLECTION).doc(courseId);
  await docRef.set(structure, { merge: true });

  revalidatePath(`/courses`);
  return JSON.parse(JSON.stringify(structure));
}
