
'use client';

import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { UserCircle, Shield, Bell, Palette, Loader2, AlertTriangle, CheckCircle, Building, BookLock, Briefcase, Image as ImageIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import { useState, useEffect, useCallback } from 'react';
import { ThemeToggle } from '@/components/settings/ThemeToggle';
import { Switch } from '@/components/ui/switch';
import { getSystemSettings, updateSystemSettings } from '@/actions/settingsActions';
import { runFirestoreHealthCheck, verifyAdminStatus } from '@/actions/healthCheckActions';
import { forceLogoutAllUsers } from '@/actions/adminActions';
import { UserRoleManager } from '@/components/settings/UserRoleManager';
import type { SystemSettings } from '@/types';
import { auth } from '@/lib/firebase';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Textarea } from '@/components/ui/textarea';

const profileFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  email: z.string().email().optional(), // Email display only, not editable here
});
type ProfileFormValues = z.infer<typeof profileFormSchema>;

const passwordFormSchema = z.object({
  currentPassword: z.string().min(6, { message: 'Current password is required.' }),
  newPassword: z.string().min(6, { message: 'New password must be at least 6 characters.' }),
  confirmPassword: z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "New passwords don't match",
  path: ['confirmPassword'],
});
type PasswordFormValues = z.infer<typeof passwordFormSchema>;

const institutionFormSchema = z.object({
  institutionName: z.string().min(3, 'Institution name must be at least 3 characters.'),
  institutionAddress: z.string().min(10, 'Address must be at least 10 characters.'),
  institutionEmail: z.string().email('Please enter a valid email address.'),
  logoUrl: z.string().url('Please enter a valid URL.').optional().or(z.literal('')),
});
type InstitutionFormValues = z.infer<typeof institutionFormSchema>;


export default function SettingsPage() {
  const { user, updateUserDisplayName, changeUserPassword, isLoading: authLoading } = useAuth();
  const { toast } = useToast();
  const [isProfileSubmitting, setIsProfileSubmitting] = useState(false);
  const [isPasswordSubmitting, setIsPasswordSubmitting] = useState(false);
  const [isInstitutionSubmitting, setIsInstitutionSubmitting] = useState(false);
  const [systemSettings, setSystemSettings] = useState<SystemSettings | null>(null);
  const [isSettingsLoading, setIsSettingsLoading] = useState(true);
  const [isHealthCheckRunning, setIsHealthCheckRunning] = useState(false);
  const [healthCheckResult, setHealthCheckResult] = useState<{ success: boolean; message: string } | null>(null);
  const [isVerifyingAdmin, setIsVerifyingAdmin] = useState(false);
  const [adminCheckResult, setAdminCheckResult] = useState<{ success: boolean; message: string } | null>(null);
  const [isForceLogoutConfirmOpen, setIsForceLogoutConfirmOpen] = useState(false);
  const [isForceLogoutRunning, setIsForceLogoutRunning] = useState(false);

  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: { name: '', email: '' },
  });

  const passwordForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: { currentPassword: '', newPassword: '', confirmPassword: '' },
  });
  
  const institutionForm = useForm<InstitutionFormValues>({
    resolver: zodResolver(institutionFormSchema),
    defaultValues: { institutionName: '', institutionAddress: '', institutionEmail: '', logoUrl: '' },
  });

  const fetchSettings = useCallback(async () => {
    if (user?.role === 'admin') {
      setIsSettingsLoading(true);
      try {
        const settings = await getSystemSettings();
        setSystemSettings(settings);
        institutionForm.reset({
            institutionName: settings.institutionName,
            institutionAddress: settings.institutionAddress,
            institutionEmail: settings.institutionEmail,
            logoUrl: settings.logoUrl || '',
        });
      } catch (error) {
        toast({ title: "Error", description: "Could not load system settings.", variant: "destructive" });
      } finally {
        setIsSettingsLoading(false);
      }
    } else {
      setIsSettingsLoading(false);
    }
  }, [user?.role, toast, institutionForm]);

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  useEffect(() => {
    if (user) {
      profileForm.reset({
        name: user.name || '',
        email: user.email || '',
      });
    }
  }, [user, profileForm]);


  const onProfileSubmit = async (data: ProfileFormValues) => {
    setIsProfileSubmitting(true);
    try {
      if (!user) throw new Error("User not found");
      await updateUserDisplayName(data.name);
      toast({
        title: 'Profile Updated',
        description: 'Your name has been successfully updated.',
      });
    } catch (error: any) {
      toast({
        title: 'Update Failed',
        description: error.message || 'Could not update profile. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProfileSubmitting(false);
    }
  };

  const onPasswordSubmit = async (data: PasswordFormValues) => {
    setIsPasswordSubmitting(true);
    try {
      await changeUserPassword(data.currentPassword, data.newPassword);
      toast({
        title: 'Password Changed',
        description: 'Your password has been successfully updated.',
      });
      passwordForm.reset();
    } catch (error: any) {
      toast({
        title: 'Password Change Failed',
        description: error.message || 'Could not change password. Please ensure your current password is correct.',
        variant: 'destructive',
      });
    } finally {
      setIsPasswordSubmitting(false);
    }
  };

  const onInstitutionSubmit = async (data: InstitutionFormValues) => {
    if (!auth.currentUser) return;
    setIsInstitutionSubmitting(true);
    try {
        const idToken = await auth.currentUser.getIdToken();
        await updateSystemSettings(data, idToken);
        toast({ title: "Success", description: "Institution details have been updated." });
    } catch (error: any) {
        toast({ title: "Error", description: error.message || "Failed to update institution details.", variant: "destructive" });
    } finally {
        setIsInstitutionSubmitting(false);
    }
  };
  
  const handleSystemSettingToggle = async (key: 'sendNewApplicationEmail' | 'applicationsOpen', checked: boolean) => {
    if (!user || !auth.currentUser) return;
    
    setSystemSettings(prev => prev ? { ...prev, [key]: checked } : null);

    try {
      const idToken = await auth.currentUser.getIdToken();
      await updateSystemSettings({ [key]: checked }, idToken);
      const settingName = key === 'sendNewApplicationEmail' ? 'New application emails' : 'Public applications';
      toast({
        title: "Settings Updated",
        description: `${settingName} have been ${checked ? 'enabled' : 'disabled'}.`,
      });
    } catch (error) {
      // Revert optimistic update
      setSystemSettings(prev => prev ? { ...prev, [key]: !checked } : null);
      toast({ title: "Error", description: `Failed to update ${key} setting.`, variant: "destructive" });
    }
  };
  
  const handleRunHealthCheck = async () => {
    if (!auth.currentUser) return;
    setIsHealthCheckRunning(true);
    setHealthCheckResult(null);
    try {
      const idToken = await auth.currentUser.getIdToken();
      const result = await runFirestoreHealthCheck(idToken);
      setHealthCheckResult(result);
    } catch (error: any) {
      setHealthCheckResult({ success: false, message: error.message || 'An unknown error occurred.' });
    } finally {
      setIsHealthCheckRunning(false);
    }
  };

  const handleRunAdminCheck = async () => {
    if (!auth.currentUser) return;
    setIsVerifyingAdmin(true);
    setAdminCheckResult(null);
    try {
      const idToken = await auth.currentUser.getIdToken();
      const result = await verifyAdminStatus(idToken);
      setAdminCheckResult(result);
    } catch (error: any) {
      setAdminCheckResult({ success: false, message: error.message || 'An unknown error occurred.' });
    } finally {
      setIsVerifyingAdmin(false);
    }
  };

  const handleForceLogout = async () => {
    if (!auth.currentUser) return;
    setIsForceLogoutRunning(true);
    try {
        const idToken = await auth.currentUser.getIdToken();
        const result = await forceLogoutAllUsers(idToken);
        toast({
            title: "Success",
            description: `Forced logout for ${result.revokedUsersCount} users. You will be logged out shortly.`,
        });
        // The user's own token will be revoked, logout will happen automatically on next auth check or refresh
        setTimeout(() => {
            window.location.reload();
        }, 3000);
    } catch (error: any) {
        toast({
            title: "Error",
            description: error.message || 'An unknown error occurred.',
            variant: "destructive",
        });
    } finally {
        setIsForceLogoutRunning(false);
        setIsForceLogoutConfirmOpen(false);
    }
  };

  if (authLoading || !user) {
    return (
      <div className="flex justify-center items-center py-10">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <>
      <div className="space-y-8 max-w-3xl mx-auto">
        <div>
          <h1 className="text-3xl font-headline font-bold tracking-tight text-primary">Settings</h1>
          <p className="text-muted-foreground">Manage your account preferences and settings.</p>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <div className="flex items-center gap-3">
              <UserCircle className="h-6 w-6 text-primary" />
              <CardTitle className="font-headline text-xl">Profile Information</CardTitle>
            </div>
            <CardDescription>Update your personal details.</CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...profileForm}>
              <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-4">
                <FormField
                  control={profileForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isProfileSubmitting} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={profileForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input {...field} disabled={true} />
                      </FormControl>
                      <FormDescription>Email address cannot be changed here.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit" className="bg-accent hover:bg-accent/90 text-accent-foreground" disabled={isProfileSubmitting}>
                  {isProfileSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Save Changes'}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>

        <Separator />

        <Card className="shadow-lg">
          <CardHeader>
            <div className="flex items-center gap-3">
              <Shield className="h-6 w-6 text-primary" />
              <CardTitle className="font-headline text-xl">Security</CardTitle>
            </div>
            <CardDescription>Manage your account security settings.</CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...passwordForm}>
              <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-4">
                <FormField
                  control={passwordForm.control}
                  name="currentPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Current Password</FormLabel>
                      <FormControl>
                        <Input type="password" {...field} disabled={isPasswordSubmitting} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={passwordForm.control}
                  name="newPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Password</FormLabel>
                      <FormControl>
                        <Input type="password" {...field} disabled={isPasswordSubmitting} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={passwordForm.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm New Password</FormLabel>
                      <FormControl>
                        <Input type="password" {...field} disabled={isPasswordSubmitting} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit" className="bg-accent hover:bg-accent/90 text-accent-foreground" disabled={isPasswordSubmitting}>
                  {isPasswordSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Change Password'}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
        
        {user.role === 'admin' && (
          <>
            <Separator />
            <Card className="shadow-lg">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Briefcase className="h-6 w-6 text-primary" />
                  <CardTitle className="font-headline text-xl">Institution Settings</CardTitle>
                </div>
                <CardDescription>Update your institution's public information and branding.</CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...institutionForm}>
                  <form onSubmit={institutionForm.handleSubmit(onInstitutionSubmit)} className="space-y-6">
                    <FormField
                      control={institutionForm.control}
                      name="institutionName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Institution Name</FormLabel>
                          <FormControl><Input {...field} disabled={isInstitutionSubmitting} /></FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={institutionForm.control}
                      name="institutionAddress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Institution Address</FormLabel>
                          <FormControl><Textarea {...field} rows={3} disabled={isInstitutionSubmitting} /></FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                     <FormField
                      control={institutionForm.control}
                      name="institutionEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Public Contact Email</FormLabel>
                          <FormControl><Input type="email" {...field} disabled={isInstitutionSubmitting} /></FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={institutionForm.control}
                      name="logoUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Logo URL</FormLabel>
                          <FormControl>
                            <div className="flex items-center gap-2">
                              <ImageIcon className="h-5 w-5 text-muted-foreground" />
                              <Input {...field} placeholder="https://example.com/logo.png" disabled={isInstitutionSubmitting} />
                            </div>
                          </FormControl>
                          <FormDescription>Paste a public URL to your institution's logo. Leave blank to use the default icon.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button type="submit" className="bg-accent hover:bg-accent/90 text-accent-foreground" disabled={isInstitutionSubmitting}>
                      {isInstitutionSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Save Institution Details'}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>

            <Separator />

            <Card className="shadow-lg">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <BookLock className="h-6 w-6 text-primary" />
                  <CardTitle className="font-headline text-xl">Enrollment Settings</CardTitle>
                </div>
                <CardDescription>Control the application and enrollment process.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 pt-4">
                  {isSettingsLoading ? (
                    <div className="flex items-center justify-between rounded-lg border p-4 shadow-sm animate-pulse">
                      <div className="space-y-1.5"><div className="h-5 w-48 rounded-md bg-muted" /><div className="h-3 w-64 rounded-md bg-muted" /></div><div className="h-6 w-11 rounded-full bg-muted" />
                    </div>
                  ) : systemSettings && (
                  <div className="flex items-center justify-between rounded-lg border p-4 shadow-sm">
                    <div className="space-y-0.5">
                      <Label htmlFor="applications-open" className="text-base">Accept New Applications</Label>
                      <p className="text-xs text-muted-foreground">Enable or disable the public application form.</p>
                    </div>
                    <Switch
                      id="applications-open"
                      checked={systemSettings.applicationsOpen}
                      onCheckedChange={(checked) => handleSystemSettingToggle('applicationsOpen', checked)}
                    />
                  </div>
                )}
              </CardContent>
            </Card>

          </>
        )}
        
        <Separator />

        <Card className="shadow-lg">
          <CardHeader>
            <div className="flex items-center gap-3">
              <Bell className="h-6 w-6 text-primary" />
              <CardTitle className="font-headline text-xl">Notifications</CardTitle>
            </div>
            <CardDescription>Manage how you receive updates from EduLite.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 pt-4">
              {user.role === 'admin' && (
                isSettingsLoading ? (
                  <div className="flex items-center justify-between rounded-lg border p-4 shadow-sm animate-pulse">
                    <div className="space-y-1.5"><div className="h-5 w-48 rounded-md bg-muted" /><div className="h-3 w-64 rounded-md bg-muted" /></div><div className="h-6 w-11 rounded-full bg-muted" />
                  </div>
                ) : systemSettings && (
                  <div className="flex items-center justify-between rounded-lg border p-4 shadow-sm">
                    <div className="space-y-0.5">
                      <Label htmlFor="application-emails" className="text-base">New Application Emails</Label>
                      <p className="text-xs text-muted-foreground">Receive an email for each new student application submitted.</p>
                    </div>
                    <Switch
                      id="application-emails"
                      checked={systemSettings.sendNewApplicationEmail}
                      onCheckedChange={(checked) => handleSystemSettingToggle('sendNewApplicationEmail', checked)}
                    />
                  </div>
                )
              )}
              
              {user.role === 'accountant' && (
                <div className="flex items-center justify-between rounded-lg border p-4 shadow-sm">
                  <div className="space-y-0.5"><Label htmlFor="application-emails-acct" className="text-base">New Application Emails</Label><p className="text-xs text-muted-foreground">This is a global setting managed by administrators.</p></div><Switch id="application-emails-acct" disabled />
                </div>
              )}

              {user.role === 'student' && (
                <div className="flex items-center justify-between rounded-lg border p-4 shadow-sm">
                  <div className="space-y-0.5"><Label htmlFor="status-emails" className="text-base">Application Status Updates</Label><p className="text-xs text-muted-foreground">Get notified by email when your application status changes.</p></div><Switch id="status-emails" checked disabled />
                </div>
              )}

              <div className="flex items-center justify-between rounded-lg border p-4 shadow-sm">
                <div className="space-y-0.5"><Label htmlFor="marketing-emails" className="text-base">Marketing & Newsletter</Label><p className="text-xs text-muted-foreground">Receive updates about new courses, events, and news from EduLite.</p></div><Switch id="marketing-emails" disabled />
              </div>
              
              <p className="text-sm text-muted-foreground pt-2 text-center italic">Additional notification controls are under development.</p>
          </CardContent>
        </Card>

        <Separator />

        <Card className="shadow-lg">
          <CardHeader>
            <div className="flex items-center gap-3">
              <Palette className="h-6 w-6 text-primary" />
              <CardTitle className="font-headline text-xl">Appearance</CardTitle>
            </div>
            <CardDescription>Customize the look and feel of the application.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ThemeToggle />
          </CardContent>
        </Card>

        {user.role === 'admin' && (
          <>
            <Separator />
            <Card className="shadow-lg border-destructive/50">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Shield className="h-6 w-6 text-destructive" />
                  <CardTitle className="font-headline text-xl text-destructive">Admin Zone</CardTitle>
                </div>
                <CardDescription>High-privilege system operations and diagnostics.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                  <div className="rounded-lg border border-dashed p-4">
                      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                          <div><h4 className="font-semibold">Admin Status Check</h4><p className="text-sm text-muted-foreground">Verify that your account has the correct 'admin' role and permissions.</p></div>
                          <Button onClick={handleRunAdminCheck} disabled={isVerifyingAdmin} className="w-full sm:w-auto">
                              {isVerifyingAdmin ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                              Run Check
                          </Button>
                      </div>
                      {adminCheckResult && (
                          <Alert variant={adminCheckResult.success ? 'default' : 'destructive'} className="mt-4">
                              {adminCheckResult.success ? <CheckCircle className="h-4 w-4" /> : <AlertTriangle className="h-4 w-4" />}
                              <AlertTitle>{adminCheckResult.success ? 'Verification Passed' : 'Verification Failed'}</AlertTitle>
                              <AlertDescription className="whitespace-pre-wrap text-xs leading-5">{adminCheckResult.message}</AlertDescription>
                          </Alert>
                      )}
                  </div>

                  <div className="rounded-lg border border-dashed p-4">
                      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                          <div><h4 className="font-semibold">Firestore Health Check</h4><p className="text-sm text-muted-foreground">Verify server's connection to the database. Run this if you cannot add or see data.</p></div>
                          <Button onClick={handleRunHealthCheck} disabled={isHealthCheckRunning} className="w-full sm:w-auto">
                              {isHealthCheckRunning ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                              Run Check
                          </Button>
                      </div>
                      {healthCheckResult && (
                          <Alert variant={healthCheckResult.success ? 'default' : 'destructive'} className="mt-4">
                              {healthCheckResult.success ? <CheckCircle className="h-4 w-4" /> : <AlertTriangle className="h-4 w-4" />}
                              <AlertTitle>{healthCheckResult.success ? 'Health Check Passed' : 'Health Check Failed'}</AlertTitle>
                              <AlertDescription className="whitespace-pre-wrap text-xs leading-5">{healthCheckResult.message}</AlertDescription>
                          </Alert>
                      )}
                  </div>

                  <div className="rounded-lg border border-dashed p-4">
                      <div className="mb-4">
                          <h4 className="font-semibold">User Role Management</h4>
                          <p className="text-sm text-muted-foreground">Assign admin, accountant, or student roles to users and view all user accounts.</p>
                      </div>
                      <UserRoleManager />
                  </div>

                  <div className="rounded-lg border border-dashed p-4">
                      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                          <div><h4 className="font-semibold">Force Logout All Users</h4><p className="text-sm text-muted-foreground">Revoke all sessions and force re-login. Use this to apply role changes immediately.</p></div>
                          <Button variant="destructive" onClick={() => setIsForceLogoutConfirmOpen(true)} disabled={isForceLogoutRunning} className="w-full sm:w-auto">
                              {isForceLogoutRunning ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                              Force Logout
                          </Button>
                      </div>
                  </div>

              </CardContent>
            </Card>
          </>
        )}
      </div>

      <AlertDialog open={isForceLogoutConfirmOpen} onOpenChange={setIsForceLogoutConfirmOpen}>
          <AlertDialogContent>
              <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>This will log out every single user from the application, including yourself. They will all need to sign in again. This is useful for ensuring role changes take effect immediately.</AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                  <AlertDialogCancel disabled={isForceLogoutRunning}>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleForceLogout} disabled={isForceLogoutRunning} className="bg-destructive hover:bg-destructive/90">
                      {isForceLogoutRunning ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                      Yes, log everyone out
                  </AlertDialogAction>
              </AlertDialogFooter>
          </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
