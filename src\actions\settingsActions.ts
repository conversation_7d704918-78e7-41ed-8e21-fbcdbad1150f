
'use server';

import { db } from '@/lib/firebase-admin';
import { requireAuth } from '@/lib/authUtils';
import type { SystemSettings } from '@/types';
import { revalidatePath } from 'next/cache';

const SETTINGS_COLLECTION = 'systemSettings';
const GLOBAL_SETTINGS_ID = 'global';

/**
 * Fetches the global system settings.
 * Creates default settings if they don't exist.
 * This can be called by other server actions.
 * @returns The system settings object.
 */
export async function getSystemSettings(): Promise<SystemSettings> {
  const docRef = db.collection(SETTINGS_COLLECTION).doc(GLOBAL_SETTINGS_ID);
  const docSnap = await docRef.get();

  if (docSnap.exists) {
    // Ensure all fields exist, providing defaults for any that might be missing in old documents
    const data = docSnap.data() as Partial<SystemSettings>;
    return {
      id: 'global',
      sendNewApplicationEmail: data.sendNewApplicationEmail ?? true,
      institutionName: data.institutionName || 'EduLite Institution',
      institutionAddress: data.institutionAddress || '123 Education Lane, Knowledge City, 54321',
      institutionEmail: data.institutionEmail || '<EMAIL>',
      logoUrl: data.logoUrl || '',
      applicationsOpen: data.applicationsOpen ?? true,
      defaultDueDateDays: data.defaultDueDateDays ?? 30,
    };
  } else {
    // If settings don't exist, create and return default settings.
    const defaultSettings: SystemSettings = {
      id: GLOBAL_SETTINGS_ID,
      sendNewApplicationEmail: true,
      institutionName: 'EduLite Institution',
      institutionAddress: '123 Education Lane, Knowledge City, 54321',
      institutionEmail: '<EMAIL>',
      logoUrl: '',
      applicationsOpen: true,
      defaultDueDateDays: 30,
    };
    await docRef.set(defaultSettings);
    return defaultSettings;
  }
}

/**
 * Updates the global system settings.
 * Requires admin privileges.
 * @param settingsToUpdate A partial object of the settings to update.
 * @param idToken The Firebase ID token of the calling admin user.
 * @returns The updated system settings object.
 */
export async function updateSystemSettings(
  settingsToUpdate: Partial<Omit<SystemSettings, 'id'>>,
  idToken: string
): Promise<SystemSettings> {
  await requireAuth(idToken, ['admin']);

  const docRef = db.collection(SETTINGS_COLLECTION).doc(GLOBAL_SETTINGS_ID);
  
  // Sanitize numeric inputs before saving
  if (settingsToUpdate.defaultDueDateDays !== undefined) {
    settingsToUpdate.defaultDueDateDays = Number(settingsToUpdate.defaultDueDateDays);
    if (isNaN(settingsToUpdate.defaultDueDateDays) || settingsToUpdate.defaultDueDateDays < 0) {
        settingsToUpdate.defaultDueDateDays = 30; // Fallback to default
    }
  }

  await docRef.set(settingsToUpdate, { merge: true });

  revalidatePath('/(app)/settings', 'page');
  revalidatePath('/apply', 'page');
  
  const updatedSettings = await getSystemSettings();
  return updatedSettings;
}
