{"name": "edulite", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "build:analyze": "cross-env ANALYZE=true next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rm -rf .next out node_modules/.cache", "dev:debug": "NODE_OPTIONS='--inspect' next dev"}, "dependencies": {"@genkit-ai/googleai": "^1.8.0", "@genkit-ai/next": "^1.8.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@react-email/components": "0.0.22", "@types/react-window": "^1.8.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "genkit": "^1.8.0", "lucide-react": "^0.475.0", "next": "15.3.3", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-email": "^2.1.3", "react-hook-form": "^7.54.2", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.15.1", "resend": "^3.5.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.5", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "cross-env": "^7.0.3", "eslint-plugin-testing-library": "^7.5.4", "firebase-tools": "^13.35.1", "genkit-cli": "^1.8.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "next-pwa": "^5.6.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "workbox-webpack-plugin": "^7.3.0"}, "overrides": {"browserslist": "4.23.0", "caniuse-lite": "1.0.30001632"}}