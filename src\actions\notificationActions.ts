
'use server';

import { db } from '@/lib/firebase-admin';
import { FieldValue } from 'firebase-admin/firestore';
import type { Notification } from '@/types';
import { requireAuth } from '@/lib/authUtils';
import { revalidatePath } from 'next/cache';

const NOTIFICATIONS_COLLECTION = 'notifications';

// This is an internal function to be called by other server actions.
// It doesn't need its own auth check as the calling action is already secured.
export async function createNotification(data: Omit<Notification, 'id' | 'createdAt' | 'read'>): Promise<Notification> {
  if (!db) {
    throw new Error('Database not initialized. Check server logs.');
  }

  const notificationPayload = {
    ...data,
    read: false,
    createdAt: FieldValue.serverTimestamp(),
  };

  const docRef = await db.collection(NOTIFICATIONS_COLLECTION).add(notificationPayload);
  const newDoc = await docRef.get();
  const newNotificationData = newDoc.data();

  if (!newNotificationData) {
    throw new Error('Could not retrieve created notification data.');
  }

  const newNotification: Notification = {
    id: docRef.id,
    ...newNotificationData,
    createdAt: newNotificationData.createdAt.toDate().toISOString(),
  } as Notification;

  // No revalidate path here, as this is internal and updates are polled on the client.
  return JSON.parse(JSON.stringify(newNotification));
}

export async function getNotifications(idToken: string): Promise<Notification[]> {
  const { uid } = await requireAuth(idToken, ['admin', 'student', 'accountant']);
  
  const snapshot = await db.collection(NOTIFICATIONS_COLLECTION)
    .where('userId', '==', uid)
    .get();

  if (snapshot.empty) {
    return [];
  }

  const notifications: Notification[] = snapshot.docs.map(doc => {
    const data = doc.data();
    return {
      id: doc.id,
      ...data,
      createdAt: data.createdAt.toDate().toISOString(),
    } as Notification;
  });

  notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  
  return JSON.parse(JSON.stringify(notifications.slice(0, 20)));
}

export async function markNotificationAsRead(notificationId: string, idToken: string): Promise<{ success: boolean }> {
  const { uid } = await requireAuth(idToken, ['admin', 'student', 'accountant']);

  const notificationRef = db.collection(NOTIFICATIONS_COLLECTION).doc(notificationId);
  const doc = await notificationRef.get();

  if (!doc.exists || doc.data()?.userId !== uid) {
    throw new Error("Notification not found or you don't have permission to modify it.");
  }
  
  await notificationRef.update({ read: true });

  return { success: true };
}

export async function markAllNotificationsAsRead(idToken: string): Promise<{ success: boolean; count: number }> {
  const { uid } = await requireAuth(idToken, ['admin', 'student', 'accountant']);

  const unreadQuery = db.collection(NOTIFICATIONS_COLLECTION)
    .where('userId', '==', uid)
    .where('read', '==', false);

  const snapshot = await unreadQuery.get();
  
  if (snapshot.empty) {
    return { success: true, count: 0 };
  }

  const batch = db.batch();
  snapshot.docs.forEach(doc => {
    batch.update(doc.ref, { read: true });
  });

  await batch.commit();

  return { success: true, count: snapshot.size };
}
