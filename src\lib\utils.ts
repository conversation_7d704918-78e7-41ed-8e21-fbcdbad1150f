import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount)
}

/**
 * Generates a numeric ID from a Firestore document ID
 * Converts alphanumeric Firestore ID to a numeric representation
 * @param firestoreId - The Firestore document ID
 * @returns A numeric string representation
 */
export function generateNumericId(firestoreId: string): string {
  // Create a hash from the Firestore ID and convert to numeric
  let hash = 0;
  for (let i = 0; i < firestoreId.length; i++) {
    const char = firestoreId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  // Convert to positive number and ensure it's at least 8 digits
  const numericId = Math.abs(hash).toString().padStart(8, '0');

  // Format as a more readable number (e.g., 12345678 becomes 1234-5678)
  return numericId.replace(/(\d{4})(\d{4})/, '$1$2');
}

/**
 * Generates a student ID from user ID
 * @param userId - The Firebase user ID
 * @returns A formatted student ID
 */
export function generateStudentId(userId: string): string {
  const numericPart = generateNumericId(userId);
  return `STU${numericPart}`;
}
