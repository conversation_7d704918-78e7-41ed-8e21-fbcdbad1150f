
'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import type { StudentFee, Course, FeeStatus, PaymentTransaction } from '@/types';
import { getFinancialReportData, getAllPaymentTransactions } from '@/actions/feeActions';
import { getCourses } from '@/actions/courseActions';
import { useAuth } from '@/contexts/AuthContext';
import { auth } from '@/lib/firebase';
import { useToast } from '@/hooks/use-toast';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup, SelectLabel } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Calendar as CalendarIcon, Loader2, Download, FilterX, PiggyBank, CircleDollarSign, BarChartHorizontal } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { format, addDays } from 'date-fns';

const feeStatuses: FeeStatus[] = ['Pending', 'Paid', 'Partially Paid', 'Overdue'];

const statusVariantMap: Record<FeeStatus, 'default' | 'secondary' | 'outline' | 'destructive'> = {
  Pending: 'default',
  'Partially Paid': 'secondary',
  Paid: 'outline',
  Overdue: 'destructive',
};

export default function FinancialReportsPage() {
  const { user, isLoading: isAuthLoading } = useAuth();
  const { toast } = useToast();

  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined); // Start with no date filter to show all records
  const [courseId, setCourseId] = useState<string>('');
  const [status, setStatus] = useState<FeeStatus | ''>('');

  const [courses, setCourses] = useState<Course[]>([]);
  const [reportData, setReportData] = useState<StudentFee[]>([]);
  const [paymentTransactions, setPaymentTransactions] = useState<PaymentTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Group courses by name and code, similar to apply page
  const groupedCourses = useMemo(() => {
    return courses.reduce<Record<string, Course[]>>((acc, course) => {
      const groupName = `${course.name} (${course.code})`;
      if (!acc[groupName]) {
        acc[groupName] = [];
      }
      acc[groupName].push(course);
      return acc;
    }, {});
  }, [courses]);

  const fetchReportData = useCallback(async () => {
    if (!user || !auth.currentUser) return;
    setIsLoading(true);
    try {
      const idToken = await auth.currentUser.getIdToken();
      const filters = {
        startDate: dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : undefined,
        endDate: dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : undefined,
        courseId: courseId || undefined,
        status: status || undefined,
      };
      const [data, transactions] = await Promise.all([
        getFinancialReportData(filters, idToken),
        getAllPaymentTransactions(idToken)
      ]);
      setReportData(data);
      setPaymentTransactions(transactions);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Could not fetch report data.";
      toast({ title: "Error", description: errorMessage, variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  }, [user, dateRange, courseId, status, toast]);

  useEffect(() => {
    async function fetchInitialCourses() {
      try {
        const fetchedCourses = await getCourses();
        setCourses(fetchedCourses);
      } catch (error) {
        toast({ title: "Error", description: "Could not load courses for filtering.", variant: "destructive" });
      }
    }
    fetchInitialCourses();
  }, [toast]);

  useEffect(() => {
    if (!isAuthLoading && user) {
        fetchReportData();
    }
  }, [fetchReportData, isAuthLoading, user]);
  
  const clearFilters = () => {
    setDateRange(undefined); // Clear date range to show all records
    setCourseId('');
    setStatus('');
    // Fetch data immediately after clearing filters
    setTimeout(() => fetchReportData(), 100);
  };
  
  const summaryStats = useMemo(() => {
    const totalDue = reportData.reduce((sum, fee) => sum + fee.amountDue, 0);
    const totalPaid = reportData.reduce((sum, fee) => sum + fee.amountPaid, 0);
    const totalOutstanding = totalDue - totalPaid;
    return { totalDue, totalPaid, totalOutstanding, recordCount: reportData.length };
  }, [reportData]);

  const exportToCSV = () => {
     if (reportData.length === 0) {
        toast({ title: "No data to export", variant: "default" });
        return;
    }
    const headers = ['ID', 'Student Name', 'Course Name', 'Fee Description', 'Amount Due', 'Amount Paid', 'Remaining', 'Due Date', 'Status', 'Last Payment Date'];
    const csvRows = [headers.join(',')];

    for (const fee of reportData) {
        const remaining = fee.amountDue - fee.amountPaid;
        const values = [
            fee.id,
            `"${fee.studentName.replace(/"/g, '""')}"`,
            `"${fee.courseName.replace(/"/g, '""')}"`,
            `"${fee.feeDescription.replace(/"/g, '""')}"`,
            fee.amountDue.toFixed(2),
            fee.amountPaid.toFixed(2),
            remaining.toFixed(2),
            fee.dueDate,
            fee.status,
            fee.lastPaymentDate || ''
        ];
        csvRows.push(values.join(','));
    }

    const csvString = csvRows.join('\n');
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `financial_report_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };


  return (
    <div className="space-y-6">
        <div>
            <h1 className="text-3xl font-headline font-bold tracking-tight text-primary">Financial Reports</h1>
            <p className="text-muted-foreground">Generate and view financial reports based on various filters.</p>
        </div>

        <Card>
            <CardHeader>
                <CardTitle>Report Filters</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
                <div className="flex flex-col gap-2">
                    <label className="text-sm font-medium">Due Date Range</label>
                    <Popover>
                        <PopoverTrigger asChild>
                            <Button
                                variant={"outline"}
                                className="justify-start text-left font-normal"
                            >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {dateRange?.from ? (
                                    dateRange.to ? (
                                        `${format(dateRange.from, "LLL dd, y")} - ${format(dateRange.to, "LLL dd, y")}`
                                    ) : (
                                        format(dateRange.from, "LLL dd, y")
                                    )
                                ) : (
                                    <span>Pick a date</span>
                                )}
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                            <Calendar mode="range" selected={dateRange} onSelect={setDateRange} initialFocus />
                        </PopoverContent>
                    </Popover>
                </div>
                 <div className="flex flex-col gap-2">
                    <label className="text-sm font-medium">Course</label>
                    <Select value={courseId || '_all_'} onValueChange={(value) => setCourseId(value === '_all_' ? '' : value)}>
                        <SelectTrigger><SelectValue placeholder="All Courses" /></SelectTrigger>
                        <SelectContent>
                            <SelectItem value="_all_">All Courses</SelectItem>
                            {Object.keys(groupedCourses).length > 0 ? (
                              Object.entries(groupedCourses).map(([groupName, coursesInGroup]) => {
                                if (coursesInGroup.length === 1) {
                                  const course = coursesInGroup[0];
                                  return (
                                    <SelectItem key={course.id} value={course.id}>
                                      {groupName}
                                    </SelectItem>
                                  );
                                } else {
                                  return (
                                    <SelectGroup key={groupName}>
                                      <SelectLabel>{groupName}</SelectLabel>
                                      {coursesInGroup.map(course => (
                                        <SelectItem key={course.id} value={course.id}>
                                          {course.specializations?.[0] || 'General'}
                                        </SelectItem>
                                      ))}
                                    </SelectGroup>
                                  );
                                }
                              })
                            ) : (
                              <SelectItem value="no-courses" disabled>No courses available</SelectItem>
                            )}
                        </SelectContent>
                    </Select>
                 </div>
                 <div className="flex flex-col gap-2">
                    <label className="text-sm font-medium">Status</label>
                    <Select value={status || '_all_'} onValueChange={(value: string) => setStatus(value === '_all_' ? '' : value as FeeStatus)}>
                        <SelectTrigger><SelectValue placeholder="All Statuses" /></SelectTrigger>
                        <SelectContent>
                            <SelectItem value="_all_">All Statuses</SelectItem>
                            {feeStatuses.map(s => <SelectItem key={s} value={s} className="capitalize">{s}</SelectItem>)}
                        </SelectContent>
                    </Select>
                 </div>
                 <div className="flex gap-2">
                    <Button onClick={fetchReportData} disabled={isLoading} className="w-full">
                        {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Apply Filters'}
                    </Button>
                     <Button onClick={clearFilters} disabled={isLoading} variant="ghost" size="icon">
                        <FilterX className="h-4 w-4" />
                        <span className="sr-only">Clear Filters</span>
                    </Button>
                 </div>
            </CardContent>
        </Card>

        <div className="grid gap-4 md:grid-cols-3">
            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Collected</CardTitle>
                    <PiggyBank className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    {isLoading ? <Skeleton className="h-8 w-32"/> : <div className="text-2xl font-bold">₹{summaryStats.totalPaid.toFixed(2)}</div>}
                </CardContent>
            </Card>
             <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Outstanding</CardTitle>
                    <CircleDollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    {isLoading ? <Skeleton className="h-8 w-32"/> : <div className="text-2xl font-bold text-destructive">₹{summaryStats.totalOutstanding.toFixed(2)}</div>}
                </CardContent>
            </Card>
             <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Filtered Records</CardTitle>
                    <BarChartHorizontal className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    {isLoading ? <Skeleton className="h-8 w-16"/> : <div className="text-2xl font-bold">{summaryStats.recordCount}</div>}
                </CardContent>
            </Card>
        </div>

        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <div>
                    <CardTitle>Filtered Fee Records</CardTitle>
                    <CardDescription>
                       A detailed list of fee records matching the selected filters. Found {reportData.length} records.
                    </CardDescription>
                </div>
                 <Button onClick={exportToCSV} disabled={isLoading || reportData.length === 0} variant="outline">
                    <Download className="mr-2 h-4 w-4" /> Export CSV
                </Button>
            </CardHeader>
            <CardContent>
                {isLoading ? (
                     <div className="space-y-2">
                        {[...Array(5)].map((_, i) => <Skeleton key={i} className="h-10 w-full" />)}
                     </div>
                ) : reportData.length === 0 ? (
                    <div className="text-center text-muted-foreground py-10">
                        <p>No records match the current filters.</p>
                    </div>
                ) : (
                    <div className="rounded-md border">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Student</TableHead>
                                    <TableHead>Course</TableHead>
                                    <TableHead>Fee Item</TableHead>
                                    <TableHead>Due Date</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead className="text-right">Amount Due</TableHead>
                                    <TableHead className="text-right">Amount Paid</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {reportData.map(fee => (
                                    <TableRow key={fee.id}>
                                        <TableCell className="font-medium">{fee.studentName}</TableCell>
                                        <TableCell>{fee.courseName}</TableCell>
                                        <TableCell>{fee.feeDescription}</TableCell>
                                        <TableCell>{format(new Date(fee.dueDate), 'PP')}</TableCell>
                                        <TableCell><Badge variant={statusVariantMap[fee.status]} className="capitalize">{fee.status}</Badge></TableCell>
                                        <TableCell className="text-right">₹{fee.amountDue.toFixed(2)}</TableCell>
                                        <TableCell className="text-right font-semibold text-green-700">₹{fee.amountPaid.toFixed(2)}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                )}
            </CardContent>
        </Card>

        {/* Payment Transactions Section */}
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <span>Payment Transaction History</span>
                        <Badge variant="outline">{paymentTransactions.length} transactions</Badge>
                    </div>
                </CardTitle>
                <CardDescription>
                    Detailed history of all payment transactions recorded in the system.
                </CardDescription>
            </CardHeader>
            <CardContent>
                {isLoading ? (
                     <div className="space-y-2">
                        {[...Array(5)].map((_, i) => <Skeleton key={i} className="h-10 w-full" />)}
                     </div>
                ) : paymentTransactions.length === 0 ? (
                    <div className="text-center text-muted-foreground py-10">
                        <p>No payment transactions found.</p>
                    </div>
                ) : (
                    <div className="rounded-md border">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Payment Date</TableHead>
                                    <TableHead>Student</TableHead>
                                    <TableHead>Fee Item</TableHead>
                                    <TableHead>Payment Method</TableHead>
                                    <TableHead className="text-right">Amount</TableHead>
                                    <TableHead>Transaction ID</TableHead>
                                    <TableHead>Recorded By</TableHead>
                                    <TableHead>Notes</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {paymentTransactions.map(transaction => (
                                    <TableRow key={transaction.id}>
                                        <TableCell className="font-medium">
                                            {format(new Date(transaction.paymentDate), 'PP')}
                                        </TableCell>
                                        <TableCell>{transaction.studentName}</TableCell>
                                        <TableCell>
                                            {reportData.find(fee => fee.id === transaction.feeId)?.feeDescription || 'N/A'}
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant="outline">{transaction.paymentMethod}</Badge>
                                        </TableCell>
                                        <TableCell className="text-right font-semibold text-green-700">
                                            ₹{transaction.amount.toFixed(2)}
                                        </TableCell>
                                        <TableCell className="text-xs text-muted-foreground">
                                            {transaction.transactionId || '-'}
                                        </TableCell>
                                        <TableCell className="text-xs text-muted-foreground">
                                            {transaction.recordedBy}
                                        </TableCell>
                                        <TableCell className="text-xs text-muted-foreground">
                                            {transaction.notes || '-'}
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                )}
            </CardContent>
        </Card>
    </div>
  );
}
