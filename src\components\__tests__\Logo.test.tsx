import { render, screen, waitFor } from '@testing-library/react'
import { Logo } from '@/components/navigation/Logo'

// Mock Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} />
  },
}))

describe('Logo Component', () => {
  it('renders the logo with EduLite branding', () => {
    render(<Logo />)

    // Should show EduLite text
    expect(screen.getByText('EduLite')).toBeInTheDocument()
    expect(screen.getByRole('link')).toBeInTheDocument()
  })

  it('renders with different sizes', () => {
    render(<Logo size="lg" />)

    const logoText = screen.getByText('EduLite')
    expect(logoText).toHaveClass('text-3xl')
  })

  it('renders as a link to home page', () => {
    render(<Logo />)

    const linkElement = screen.getByRole('link')
    expect(linkElement).toHaveAttribute('href', '/')
  })

  it('always shows EduLite branding regardless of settings', () => {
    render(<Logo />)

    // Should always show EduLite, not institution name
    expect(screen.getByText('EduLite')).toBeInTheDocument()
  })
})
