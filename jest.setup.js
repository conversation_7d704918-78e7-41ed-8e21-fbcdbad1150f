import '@testing-library/jest-dom'
import React from 'react'

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  GraduationCap: (props) => React.createElement('div', { 'data-testid': 'graduation-cap-icon', ...props }),
  Loader2: (props) => React.createElement('div', { 'data-testid': 'loader-icon', ...props }),
  LayoutDashboard: (props) => React.createElement('div', { 'data-testid': 'dashboard-icon', ...props }),
  BookOpen: (props) => React.createElement('div', { 'data-testid': 'book-icon', ...props }),
  Users: (props) => React.createElement('div', { 'data-testid': 'users-icon', ...props }),
  Bot: (props) => React.createElement('div', { 'data-testid': 'bot-icon', ...props }),
  FileText: (props) => React.createElement('div', { 'data-testid': 'file-icon', ...props }),
  Settings: (props) => React.createElement('div', { 'data-testid': 'settings-icon', ...props }),
  DollarSign: (props) => React.createElement('div', { 'data-testid': 'dollar-icon', ...props }),
  AreaChart: (props) => React.createElement('div', { 'data-testid': 'chart-icon', ...props }),
  UserCheck: (props) => React.createElement('div', { 'data-testid': 'user-check-icon', ...props }),
  Menu: (props) => React.createElement('div', { 'data-testid': 'menu-icon', ...props }),
}))

// Mock Firebase
jest.mock('@/lib/firebase', () => ({
  auth: {
    currentUser: null,
    onAuthStateChanged: jest.fn(),
    signInWithEmailAndPassword: jest.fn(),
    signOut: jest.fn(),
  },
  analytics: undefined,
}))

// Mock Firebase Admin
jest.mock('@/lib/firebase-admin', () => ({
  db: {
    collection: jest.fn(() => ({
      add: jest.fn(),
      doc: jest.fn(() => ({
        get: jest.fn(),
        set: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      })),
      where: jest.fn(() => ({
        get: jest.fn(),
        orderBy: jest.fn(() => ({
          get: jest.fn(),
        })),
      })),
      orderBy: jest.fn(() => ({
        get: jest.fn(),
      })),
      get: jest.fn(),
    })),
  },
  adminAuth: {
    verifyIdToken: jest.fn(),
  },
}))

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
}))

// Mock server actions
jest.mock('@/actions/settingsActions', () => ({
  getSystemSettings: jest.fn(() => Promise.resolve({
    id: 'global',
    institutionName: 'EduLite Test',
    institutionEmail: '<EMAIL>',
    institutionAddress: 'Test Address',
    logoUrl: null,
    sendNewApplicationEmail: true,
    applicationsOpen: true,
    defaultDueDateDays: 30,
  })),
}))

// Mock environment variables
process.env.GOOGLE_API_KEY = 'test-api-key'
process.env.ADMIN_EMAIL = '<EMAIL>'

// Global test utilities
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})
