
'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

import { NavLinks } from '@/components/navigation/NavLinks';
import { Loader2 } from 'lucide-react';
import {
  SidebarProvider,
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarFooter,
  SidebarInset
} from "@/components/ui/sidebar";
import { Logo } from '@/components/navigation/Logo';
import { UserProfileSection } from '@/components/navigation/UserProfileSection';

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login');
    }
  }, [user, isLoading, router]);

  if (isLoading || !user) {
    return (
      <div className="flex h-screen items-center justify-center bg-background">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <SidebarProvider defaultOpen>
      <Sidebar side="left" variant="sidebar" collapsible="icon" className="no-print">
        <SidebarHeader className="border-b">
            <div className="flex h-16 items-center px-4">
              <Logo size="md" />
            </div>
        </SidebarHeader>
        <SidebarContent className="p-2">
          <NavLinks />
        </SidebarContent>
        <SidebarFooter className="p-2 border-t">
          <UserProfileSection />
        </SidebarFooter>
      </Sidebar>
      
      <SidebarInset> {/* SidebarInset is a <main> tag itself and handles flex-1 for horizontal space */}
        {/* Removed AppHeader for clean experience */}
        <div className="flex-1 p-4 md:p-6 lg:p-8 overflow-y-auto">
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
