
'use server';

import { revalidatePath } from 'next/cache';
import { db } from '@/lib/firebase-admin';
import { requireAuth } from '@/lib/authUtils';
import type { Lead } from '@/types';
import { FieldValue } from 'firebase-admin/firestore';

const LEADS_COLLECTION = 'leads';

/**
 * Fetches all leads from the Firestore database.
 * Requires admin privileges.
 * @param idToken The Firebase ID token of the calling admin user.
 * @returns A promise that resolves to an array of leads.
 */
export async function getLeads(idToken: string): Promise<Lead[]> {
  await requireAuth(idToken, ['admin']);

  if (!db) {
    throw new Error('Database not initialized. Check server logs.');
  }

  try {
    const leadsSnapshot = await db.collection(LEADS_COLLECTION).get();
    
    if (leadsSnapshot.empty) {
      return [];
    }
    
    const leads: Lead[] = leadsSnapshot.docs.map(doc => {
      const data = doc.data();
      
      // Defensively format the inquiryDate to prevent crashes from corrupted data
      let formattedDate = new Date().toISOString().split('T')[0]; // Default to today if date is invalid
      if (data.inquiryDate) {
        if (data.inquiryDate.toDate) { // It's a valid Firestore Timestamp
          formattedDate = data.inquiryDate.toDate().toISOString().split('T')[0];
        } else if (typeof data.inquiryDate === 'string') { // It's a string (already converted or corrupted)
          // Ensure it's in the correct YYYY-MM-DD format before using
          const parsedDate = new Date(data.inquiryDate);
          if (!isNaN(parsedDate.getTime())) {
            formattedDate = parsedDate.toISOString().split('T')[0];
          }
        }
      }

      return {
        id: doc.id,
        name: data.name,
        email: data.email,
        phone: data.phone,
        inquiryDate: formattedDate,
        source: data.source,
        status: data.status,
        desiredCourse: data.desiredCourse,
        notes: data.notes,
      } as Lead;
    });

    // Sort the leads by date in descending order after fetching them.
    leads.sort((a, b) => new Date(b.inquiryDate).getTime() - new Date(a.inquiryDate).getTime());
    
    return JSON.parse(JSON.stringify(leads));

  } catch (error: any) {
    // A NOT_FOUND error (code 5) can occur if the collection does not exist yet.
    // This is a normal condition for a new app, so we'll treat it as an empty list.
    if (error.code === 5) {
      console.log("Leads collection not found. Returning empty array. This is expected if no leads have been created yet.");
      return [];
    }
    // For any other errors, we should re-throw them.
    console.error("Error fetching leads from Firestore:", error);
    throw new Error("An unexpected error occurred while fetching leads.");
  }
}

/**
 * Creates a new lead in the Firestore database.
 * This action is public to support the QR code lead capture form.
 * @param leadData The data for the new lead.
 * @returns A promise that resolves to the newly created lead.
 */
export async function createLead(leadData: Omit<Lead, 'id' | 'inquiryDate' | 'status'>): Promise<Lead> {
  if (!db) {
    throw new Error('Database not initialized. Check server logs.');
  }

  const newLeadData = {
    ...leadData,
    inquiryDate: FieldValue.serverTimestamp(), // Use server timestamp for accuracy
    status: 'New', // Default status for new leads
  };

  const leadRef = await db.collection(LEADS_COLLECTION).add(newLeadData);
  
  // Fetch the just-created document to get the server-generated timestamp
  const newDocSnapshot = await leadRef.get();
  const createdLeadData = newDocSnapshot.data();

  if (!createdLeadData) {
    throw new Error("Could not retrieve created lead data.");
  }

  const createdLead: Lead = {
      id: leadRef.id,
      name: createdLeadData.name,
      email: createdLeadData.email,
      phone: createdLeadData.phone,
      inquiryDate: createdLeadData.inquiryDate.toDate().toISOString().split('T')[0],
      source: createdLeadData.source,
      status: createdLeadData.status,
      desiredCourse: createdLeadData.desiredCourse,
      notes: createdLeadData.notes,
  };

  revalidatePath('/dashboard');
  return JSON.parse(JSON.stringify(createdLead));
}

/**
 * Updates an existing lead in the Firestore database.
 * Requires admin privileges.
 * @param leadData The complete lead object with updated data.
 * @param idToken The Firebase ID token of the calling admin user.
 * @returns A promise that resolves to the updated lead, or null if not found.
 */
export async function updateLead(leadData: Lead, idToken: string): Promise<Lead | null> {
  await requireAuth(idToken, ['admin']);

  // Destructure to separate the ID and the inquiryDate, which should NOT be updated.
  const { id, inquiryDate, ...dataToUpdate } = leadData;
  const leadRef = db.collection(LEADS_COLLECTION).doc(id);
  
  // Update the document in Firestore with only the fields that are safe to change.
  // This prevents the inquiryDate (Timestamp) from being overwritten with a string.
  await leadRef.update(dataToUpdate);

  revalidatePath('/dashboard');
  // Return the full leadData object (including the original inquiryDate)
  // so the UI can update optimistically without needing a full re-fetch.
  return JSON.parse(JSON.stringify(leadData));
}

/**
 * Deletes a lead from the Firestore database.
 * Requires admin privileges.
 * @param leadId The ID of the lead to delete.
 * @param idToken The Firebase ID token of the calling admin user.
 * @returns A promise that resolves to an object indicating success.
 */
export async function deleteLead(leadId: string, idToken: string): Promise<{ success: boolean }> {
  await requireAuth(idToken, ['admin']);
  
  await db.collection(LEADS_COLLECTION).doc(leadId).delete();
  
  revalidatePath('/dashboard');
  return { success: true };
}
