

export interface StudyMaterial {
  id: string;
  name: string;
  type: 'PDF' | 'Video' | 'Link' | 'Document' | 'Text';
  url?: string;
  content?: string;
}

export interface Unit {
  id: string;
  name: string;
  materials?: StudyMaterial[];
}

export interface Subject {
  id: string;
  name:string;
  credits: number;
  units?: Unit[];
}

export interface Semester {
  id: string;
  semesterNumber: number;
  subjects: Subject[];
}

export interface EducationDetails {
  tenthCompletionYear?: string;
  twelfthCompletionYear?: string;
  diplomaCompletionYear?: string;
  graduateCompletionYear?: string;
  mastersCompletionYear?: string;
}

export interface StudentApplication {
  id: string;
  fullName: string;
  email: string;
  mobileNumber: string;
  motherName: string;
  fatherName: string;
  dateOfBirth: string; // YYYY-MM-DD
  religion: string;
  desiredCourse: string; // This is courseId
  specialization?: string;
  previousEducation: string[];
  reference: string;
  applicationDate: string;
  status: 'Pending' | 'Reviewed' | 'Accepted' | 'Rejected';
  userId?: string; // This will be the studentId for fees
  educationDetails?: EducationDetails;
}

export interface EnrolledStudent {
    id: string; // This will be the student's UID from Firebase Auth
    applicationId: string;
    name: string;
    email: string;
    courseId: string;
    courseName: string;
    admissionDate: string; // The date the application was submitted
}

export interface Lead {
  id: string;
  name: string;
  email: string;
  phone: string;
  inquiryDate: string;
  source: string; // e.g., 'Website Inquiry', 'Referral'
  status: 'New' | 'Contacted' | 'Qualified' | 'Lost';
  desiredCourse?: string; // This will be courseId
  notes?: string;
}

export interface Course {
  id:string;
  name: string;
  code: string;
  specializations?: string[];
  instructor: string;
  startDate?: string;
  endDate?: string;
  semesters: Semester[];
}

export interface User {
  uid: string;
  name: string | null;
  email: string | null;
  phone?: string | null;
  role: 'admin' | 'student' | 'accountant';
}

export type FeeStatus = 'Pending' | 'Paid' | 'Partially Paid' | 'Overdue';

export interface StudentFee {
  id: string;
  studentId: string;
  studentName: string; 
  courseId: string;
  courseName: string; 
  feeDescription: string; 
  amountDue: number;
  amountPaid: number;
  dueDate: string; // YYYY-MM-DD
  status: FeeStatus;
  paymentDate?: string; // YYYY-MM-DD, for fully paid fees
  lastPaymentDate?: string; // YYYY-MM-DD for partially paid fees
  tuitionFee?: number;
  examFee?: number;
  otherFee?: number;
}

export interface FeeItem {
  id: string;
  name: string;
  tuitionFee: number;
  examFee: number;
  otherFee: number;
}

export interface CourseFeeStructure {
  id: string; // Typically same as courseId for simplicity in mock, or a new UUID
  courseId: string;
  items: FeeItem[];
}

export interface PaymentTransaction {
  id: string;
  feeId: string; // Reference to StudentFee
  studentId: string;
  studentName: string;
  amount: number;
  paymentDate: string; // YYYY-MM-DD
  paymentMethod: 'Cash' | 'Online' | 'Bank Transfer' | 'Cheque' | 'UPI' | 'Card';
  transactionId: string | null; // For online payments
  notes: string | null;
  recordedBy: string; // Admin/Accountant who recorded it
  recordedAt: string; // Timestamp when recorded
}

export interface SystemSettings {
  id: 'global';
  sendNewApplicationEmail: boolean;
  institutionName: string;
  institutionAddress: string;
  institutionEmail: string;
  logoUrl?: string;
  applicationsOpen: boolean;
  defaultDueDateDays: number;
}

export interface StudentCourseProgress {
  id?: string; // Firestore doc ID
  studentId: string;
  courseId: string;
  completedUnits: string[]; // Array of unit IDs
}

export interface Notification {
  id: string;
  userId: string;
  message: string;
  read: boolean;
  createdAt: string; // ISO string for dates from Firestore
  link?: string;
}
