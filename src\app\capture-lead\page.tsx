
import { LeadCaptureForm } from './LeadCaptureForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Logo } from '@/components/navigation/Logo';
import Link from 'next/link';
import { getCourses } from '@/actions/courseActions';
import type { Course } from '@/types';

export default async function CaptureLeadPage() {
  let courses: Course[] = [];

  try {
    courses = await getCourses();
  } catch (error) {
    console.log('Could not fetch courses during build time, will load client-side');
    // During build time, Firebase might not be available, so we'll handle this gracefully
    courses = [];
  }

  return (
    <div className="min-h-screen bg-background py-8 px-4 flex flex-col items-center justify-center">
      <div className="mb-8">
        <Logo size="lg" />
      </div>
      <Card className="w-full max-w-md shadow-xl">
        <CardHeader className="text-center">
          <CardTitle className="font-headline text-3xl text-primary">Submit Your Inquiry</CardTitle>
          <CardDescription>Interested in our courses? Let us know how to reach you.</CardDescription>
        </CardHeader>
        <CardContent>
          <LeadCaptureForm courses={courses} />
        </CardContent>
      </Card>
       <p className="mt-8 text-center text-sm text-muted-foreground">
        Are you an existing user?{' '}
        <Link href="/login" className="font-medium text-accent hover:underline">
          Sign In
        </Link>
      </p>
    </div>
  );
}
